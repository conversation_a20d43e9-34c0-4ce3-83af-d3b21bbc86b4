{"version": 3, "file": "index.js", "sources": ["../src/index.ts"], "sourcesContent": ["import axios, {\n  AxiosError,\n  AxiosInstance,\n  AxiosRequestConfig,\n  AxiosResponse,\n} from 'axios';\n\n/**\n * Configuration for the Axios `request` method.\n */\nexport interface RetryConfig {\n  /**\n   * The number of times to retry the request.  Defaults to 3.\n   */\n  retry?: number;\n\n  /**\n   * The number of retries already attempted.\n   */\n  currentRetryAttempt?: number;\n\n  /**\n   * The amount of time to initially delay the retry.  Defaults to 100.\n   */\n  retryDelay?: number;\n\n  /**\n   * The instance of the axios object to which the interceptor is attached.\n   */\n  instance?: AxiosInstance;\n\n  /**\n   * The HTTP Methods that will be automatically retried.\n   * Defaults to ['GET','PUT','HEAD','OPTIONS','DELETE']\n   */\n  httpMethodsToRetry?: string[];\n\n  /**\n   * The HTTP response status codes that will automatically be retried.\n   * Defaults to: [[100, 199], [429, 429], [500, 599]]\n   */\n  statusCodesToRetry?: number[][];\n\n  /**\n   * Function to invoke when a retry attempt is made.\n   */\n  onRetryAttempt?: (err: AxiosError) => void;\n\n  /**\n   * Function to invoke which determines if you should retry\n   */\n  shouldRetry?: (err: AxiosError) => boolean;\n\n  /**\n   * When there is no response, the number of retries to attempt. Defaults to 2.\n   */\n  noResponseRetries?: number;\n\n  /**\n   * Backoff Type; 'linear', 'static' or 'exponential'.\n   */\n  backoffType?: 'linear' | 'static' | 'exponential';\n\n  /**\n   * Whether to check for 'Retry-After' header in response and use value as delay. Defaults to true.\n   */\n  checkRetryAfter?: boolean;\n\n  /**\n   * Max permitted Retry-After value (in ms) - rejects if greater. Defaults to 5 mins.\n   */\n  maxRetryAfter?: number;\n\n  /**\n   * Ceiling for calculated delay (in ms) - delay will not exceed this value.\n   */\n  maxRetryDelay?: number;\n}\n\nexport type RaxConfig = {\n  raxConfig: RetryConfig;\n} & AxiosRequestConfig;\n\n/**\n * Attach the interceptor to the Axios instance.\n * @param instance The optional Axios instance on which to attach the\n * interceptor.\n * @returns The id of the interceptor attached to the axios instance.\n */\nexport function attach(instance?: AxiosInstance) {\n  instance = instance || axios;\n  return instance.interceptors.response.use(onFulfilled, onError);\n}\n\n/**\n * Eject the Axios interceptor that is providing retry capabilities.\n * @param interceptorId The interceptorId provided in the config.\n * @param instance The axios instance using this interceptor.\n */\nexport function detach(interceptorId: number, instance?: AxiosInstance) {\n  instance = instance || axios;\n  instance.interceptors.response.eject(interceptorId);\n}\n\nfunction onFulfilled(res: AxiosResponse) {\n  return res;\n}\n\n/**\n * Some versions of axios are converting arrays into objects during retries.\n * This will attempt to convert an object with the following structure into\n * an array, where the keys correspond to the indices:\n * {\n *   0: {\n *     // some property\n *   },\n *   1: {\n *     // another\n *   }\n * }\n * @param obj The object that (may) have integers that correspond to an index\n * @returns An array with the pucked values\n */\nfunction normalizeArray<T>(obj?: T[]): T[] | undefined {\n  const arr: T[] = [];\n  if (!obj) {\n    return undefined;\n  }\n  if (Array.isArray(obj)) {\n    return obj;\n  }\n  if (typeof obj === 'object') {\n    Object.keys(obj).forEach(key => {\n      if (typeof key === 'number') {\n        arr[key] = obj[key];\n      }\n    });\n  }\n  return arr;\n}\n\n/**\n * Parse the Retry-After header.\n * https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Retry-After\n * @param header Retry-After header value\n * @returns Number of milliseconds, or undefined if invalid\n */\nfunction parseRetryAfter(header: string): number | undefined {\n  // Header value may be string containing integer seconds\n  const value = Number(header);\n  if (!Number.isNaN(value)) {\n    return value * 1000;\n  }\n  // Or HTTP date time string\n  const dateTime = Date.parse(header);\n  if (!Number.isNaN(dateTime)) {\n    return dateTime - Date.now();\n  }\n  return undefined;\n}\n\nfunction onError(err: AxiosError) {\n  if (axios.isCancel(err)) {\n    return Promise.reject(err);\n  }\n\n  const config = getConfig(err) || {};\n  config.currentRetryAttempt = config.currentRetryAttempt || 0;\n  config.retry = typeof config.retry === 'number' ? config.retry : 3;\n  config.retryDelay =\n    typeof config.retryDelay === 'number' ? config.retryDelay : 100;\n  config.instance = config.instance || axios;\n  config.backoffType = config.backoffType || 'exponential';\n  config.httpMethodsToRetry = normalizeArray(config.httpMethodsToRetry) || [\n    'GET',\n    'HEAD',\n    'PUT',\n    'OPTIONS',\n    'DELETE',\n  ];\n  config.noResponseRetries =\n    typeof config.noResponseRetries === 'number' ? config.noResponseRetries : 2;\n  config.checkRetryAfter =\n    typeof config.checkRetryAfter === 'boolean' ? config.checkRetryAfter : true;\n  config.maxRetryAfter =\n    typeof config.maxRetryAfter === 'number' ? config.maxRetryAfter : 60000 * 5;\n\n  // If this wasn't in the list of status codes where we want\n  // to automatically retry, return.\n  const retryRanges = [\n    // https://en.wikipedia.org/wiki/List_of_HTTP_status_codes\n    // 1xx - Retry (Informational, request still processing)\n    // 2xx - Do not retry (Success)\n    // 3xx - Do not retry (Redirect)\n    // 4xx - Do not retry (Client errors)\n    // 429 - Retry (\"Too Many Requests\")\n    // 5xx - Retry (Server errors)\n    [100, 199],\n    [429, 429],\n    [500, 599],\n  ];\n  config.statusCodesToRetry =\n    normalizeArray(config.statusCodesToRetry) || retryRanges;\n\n  // Put the config back into the err\n  err.config = err.config || {}; // allow for wider range of errors\n  (err.config as RaxConfig).raxConfig = {...config};\n\n  // Determine if we should retry the request\n  const shouldRetryFn = config.shouldRetry || shouldRetryRequest;\n  if (!shouldRetryFn(err)) {\n    return Promise.reject(err);\n  }\n\n  // Create a promise that invokes the retry after the backOffDelay\n  const onBackoffPromise = new Promise((resolve, reject) => {\n    let delay = 0;\n    // If enabled, check for 'Retry-After' header in response to use as delay\n    if (\n      config.checkRetryAfter &&\n      err.response &&\n      err.response.headers['retry-after']\n    ) {\n      const retryAfter = parseRetryAfter(err.response.headers['retry-after']);\n      if (retryAfter && retryAfter > 0 && retryAfter <= config.maxRetryAfter!) {\n        delay = retryAfter;\n      } else {\n        return reject(err);\n      }\n    }\n\n    // Now it's certain that a retry is supposed to happen. Incremenent the\n    // counter, critical for linear and exp backoff delay calc. Note that\n    // `config.currentRetryAttempt` is local to this function whereas\n    // `(err.config as RaxConfig).raxConfig` is state that is tranferred across\n    // retries. That is, we want to mutate `(err.config as\n    // RaxConfig).raxConfig`. Another important note is about the definition of\n    // `currentRetryAttempt`: When we are here becasue the first and actual\n    // HTTP request attempt failed then `currentRetryAttempt` is still zero. We\n    // have found that a retry is indeed required. Since that is (will be)\n    // indeed the first retry it makes sense to now increase\n    // `currentRetryAttempt` by 1. So that it is in fact 1 for the first retry\n    // (as opposed to 0 or 2); an intuitive convention to use for the math\n    // below.\n    (err.config as RaxConfig).raxConfig!.currentRetryAttempt! += 1;\n\n    // store with shorter and more expressive variable name.\n    const retrycount = (err.config as RaxConfig).raxConfig!\n      .currentRetryAttempt!;\n\n    // Calculate delay according to chosen strategy\n    // Default to exponential backoff - formula: ((2^c - 1) / 2) * 1000\n    if (delay === 0) {\n      // was not set by Retry-After logic\n      if (config.backoffType === 'linear') {\n        // The delay between the first (actual) attempt and the first retry\n        // should be non-zero. Rely on the convention that `retrycount` is\n        // equal to 1 for the first retry when we are in here (was once 0,\n        // which was a bug -- see #122).\n        delay = retrycount * 1000;\n      } else if (config.backoffType === 'static') {\n        delay = config.retryDelay!;\n      } else {\n        delay = ((Math.pow(2, retrycount) - 1) / 2) * 1000;\n      }\n      if (typeof config.maxRetryDelay === 'number') {\n        delay = Math.min(delay, config.maxRetryDelay);\n      }\n    }\n    setTimeout(resolve, delay);\n  });\n\n  // Notify the user if they added an `onRetryAttempt` handler\n  const onRetryAttemptPromise = config.onRetryAttempt\n    ? Promise.resolve(config.onRetryAttempt(err))\n    : Promise.resolve();\n\n  // Return the promise in which recalls axios to retry the request\n  return Promise.resolve()\n    .then(() => onBackoffPromise)\n    .then(() => onRetryAttemptPromise)\n    .then(() => config.instance!.request(err.config));\n}\n\n/**\n * Determine based on config if we should retry the request.\n * @param err The AxiosError passed to the interceptor.\n */\nexport function shouldRetryRequest(err: AxiosError) {\n  const config = (err.config as RaxConfig).raxConfig;\n\n  // If there's no config, or retries are disabled, return.\n  if (!config || config.retry === 0) {\n    return false;\n  }\n\n  // Check if this error has no response (ETIMEDOUT, ENOTFOUND, etc)\n  if (\n    !err.response &&\n    (config.currentRetryAttempt || 0) >= config.noResponseRetries!\n  ) {\n    return false;\n  }\n\n  // Only retry with configured HttpMethods.\n  if (\n    !err.config.method ||\n    config.httpMethodsToRetry!.indexOf(err.config.method.toUpperCase()) < 0\n  ) {\n    return false;\n  }\n\n  // If this wasn't in the list of status codes where we want\n  // to automatically retry, return.\n  if (err.response && err.response.status) {\n    let isInRange = false;\n    for (const [min, max] of config.statusCodesToRetry!) {\n      const status = err.response.status;\n      if (status >= min && status <= max) {\n        isInRange = true;\n        break;\n      }\n    }\n    if (!isInRange) {\n      return false;\n    }\n  }\n\n  // If we are out of retry attempts, return\n  config.currentRetryAttempt = config.currentRetryAttempt || 0;\n  if (config.currentRetryAttempt >= config.retry!) {\n    return false;\n  }\n\n  return true;\n}\n\n/**\n * Acquire the raxConfig object from an AxiosError if available.\n * @param err The Axios error with a config object.\n */\nexport function getConfig(err: AxiosError) {\n  if (err && err.config) {\n    return (err.config as RaxConfig).raxConfig;\n  }\n  return;\n}\n\n// Include this so `config.raxConfig` works easily.\n// See https://github.com/JustinBeckwith/retry-axios/issues/64.\ndeclare module 'axios' {\n  export interface AxiosRequestConfig {\n    raxConfig?: RetryConfig;\n  }\n}\n"], "names": ["onFulfilled", "res", "normalizeArray", "obj", "arr", "Array", "isArray", "Object", "keys", "for<PERSON>ach", "key", "onError", "err", "axios", "isCancel", "Promise", "reject", "config", "getConfig", "currentRetryAttempt", "retry", "retry<PERSON><PERSON><PERSON>", "instance", "backoffType", "httpMethodsToRetry", "noResponseRetries", "checkRetryAfter", "maxRetryAfter", "statusCodesToRetry", "raxConfig", "shouldRetry", "shouldRetryRequest", "onBackoffPromise", "resolve", "delay", "response", "headers", "retryAfter", "header", "value", "Number", "isNaN", "dateTime", "Date", "parse", "now", "parseRetryAfter", "retrycount", "Math", "pow", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "min", "setTimeout", "onRetryAttemptPromise", "onRetryAttempt", "then", "request", "method", "indexOf", "toUpperCase", "status", "isInRange", "const", "interceptors", "use", "interceptorId", "eject"], "mappings": "4EAwGA,SAASA,EAAYC,UACZA,EAkBT,SAASC,EAAkBC,OACnBC,EAAW,MACZD,SAGDE,MAAMC,QAAQH,GACTA,GAEU,iBAARA,GACTI,OAAOC,KAAKL,GAAKM,iBAAQC,GACJ,iBAARA,IACTN,EAAIM,GAAOP,EAAIO,MAIdN,GAuBT,SAASO,EAAQC,MACXC,EAAMC,SAASF,UACVG,QAAQC,OAAOJ,OAGlBK,EAASC,EAAUN,IAAQ,MACjCK,EAAOE,oBAAsBF,EAAOE,qBAAuB,EAC3DF,EAAOG,MAAgC,iBAAjBH,EAAOG,MAAqBH,EAAOG,MAAQ,EACjEH,EAAOI,WACwB,iBAAtBJ,EAAOI,WAA0BJ,EAAOI,WAAa,IAC9DJ,EAAOK,SAAWL,EAAOK,UAAYT,EACrCI,EAAOM,YAAcN,EAAOM,aAAe,cAC3CN,EAAOO,mBAAqBtB,EAAee,EAAOO,qBAAuB,CACvE,MACA,OACA,MACA,UACA,UAEFP,EAAOQ,kBAC+B,iBAA7BR,EAAOQ,kBAAiCR,EAAOQ,kBAAoB,EAC5ER,EAAOS,gBAC6B,kBAA3BT,EAAOS,iBAAgCT,EAAOS,gBACvDT,EAAOU,cAC2B,iBAAzBV,EAAOU,cAA6BV,EAAOU,cAAgB,IAgBpEV,EAAOW,mBACL1B,EAAee,EAAOW,qBAbJ,EAQjB,IAAK,KACN,CAAC,IAAK,KACN,CAAC,IAAK,MAMRhB,EAAIK,OAASL,EAAIK,QAAU,GAC1BL,EAAIK,OAAqBY,UAAYtB,iBAAIU,KAGpBA,EAAOa,aAAeC,GACzBnB,UACVG,QAAQC,OAAOJ,OAIlBoB,EAAmB,IAAIjB,iBAASkB,EAASjB,OACzCkB,EAAQ,KAGVjB,EAAOS,iBACPd,EAAIuB,UACJvB,EAAIuB,SAASC,QAAQ,eACrB,KACMC,EA5EZ,SAAyBC,OAEjBC,EAAQC,OAAOF,OAChBE,OAAOC,MAAMF,UACD,IAARA,MAGHG,EAAWC,KAAKC,MAAMN,UACvBE,OAAOC,MAAMC,UACTA,EAAWC,KAAKE,MAmEFC,CAAgBlC,EAAIuB,SAASC,QAAQ,qBACpDC,GAAcA,EAAa,GAAKA,GAAcpB,EAAOU,sBAGhDX,EAAOJ,GAFdsB,EAAQG,EAmBXzB,EAAIK,OAAqBY,UAAWV,qBAAwB,MAGvD4B,EAAcnC,EAAIK,OAAqBY,UAC1CV,oBAIW,IAAVe,IAOAA,EALyB,WAAvBjB,EAAOM,YAKY,IAAbwB,EACwB,WAAvB9B,EAAOM,YACRN,EAAOI,YAEL2B,KAAKC,IAAI,EAAGF,GAAc,GAAK,EAAK,IAEZ,iBAAzB9B,EAAOiC,gBAChBhB,EAAQc,KAAKG,IAAIjB,EAAOjB,EAAOiC,iBAGnCE,WAAWnB,EAASC,KAIhBmB,EAAwBpC,EAAOqC,eACjCvC,QAAQkB,QAAQhB,EAAOqC,eAAe1C,IACtCG,QAAQkB,iBAGLlB,QAAQkB,UACZsB,uBAAWvB,IACXuB,uBAAWF,IACXE,uBAAWtC,EAAOK,SAAUkC,QAAQ5C,EAAIK,mBAO7Bc,EAAmBnB,OAC3BK,EAAUL,EAAIK,OAAqBY,cAGpCZ,GAA2B,IAAjBA,EAAOG,aACb,MAKNR,EAAIuB,WACJlB,EAAOE,qBAAuB,IAAMF,EAAOQ,yBAErC,MAKNb,EAAIK,OAAOwC,QACZxC,EAAOO,mBAAoBkC,QAAQ9C,EAAIK,OAAOwC,OAAOE,eAAiB,SAE/D,KAKL/C,EAAIuB,UAAYvB,EAAIuB,SAASyB,OAAQ,SACnCC,GAAY,QACS5C,EAAOW,mCAAqB,CAAhDkC,WACGF,EAAShD,EAAIuB,SAASyB,UACxBA,SAAiBA,QAAe,CAClCC,GAAY,aAIXA,SACI,SAKX5C,EAAOE,oBAAsBF,EAAOE,qBAAuB,IACvDF,EAAOE,qBAAuBF,EAAOG,gBAW3BF,EAAUN,MACpBA,GAAOA,EAAIK,cACLL,EAAIK,OAAqBY,kCA9PdP,UACrBA,EAAWA,GAAYT,GACPkD,aAAa5B,SAAS6B,IAAIhE,EAAaW,4BAQlCsD,EAAuB3C,IAC5CA,EAAWA,GAAYT,GACdkD,aAAa5B,SAAS+B,MAAMD"}
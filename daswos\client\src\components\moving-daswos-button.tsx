import React, { useState, useEffect, useCallback } from 'react';
import { Button } from './ui/button';

interface MovingDaswosButtonProps {
  className?: string;
}

const MovingDaswosButton: React.FC<MovingDaswosButtonProps> = ({
  className = ''
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const handleSpeakToDaswos = () => {
    // Trigger voice recording for Daswos AI
    const speakEvent = new CustomEvent('startDaswosVoice');
    window.dispatchEvent(speakEvent);

    // Hide the button after clicking
    setIsVisible(false);
  };

  // Generate random position on screen
  const generateRandomPosition = useCallback(() => {
    const buttonSize = 64; // 16 * 4 = 64px (h-16 w-16)
    const margin = 40; // Larger margin from edges to avoid UI overlap

    // Avoid the top area where header might be
    const topMargin = 100;
    // Avoid the bottom-right area where other buttons are
    const bottomRightExclusion = 200;

    const maxX = window.innerWidth - buttonSize - margin - bottomRightExclusion;
    const maxY = window.innerHeight - buttonSize - margin;

    const x = Math.random() * (maxX - margin) + margin;
    const y = Math.random() * (maxY - topMargin) + topMargin;

    return { x, y };
  }, []);

  // Show button at random position
  const showButton = useCallback(() => {
    if (!isRobotActive) {
      const newPosition = generateRandomPosition();
      setPosition(newPosition);
      setIsVisible(true);

      // Hide button after 10 seconds if not clicked
      setTimeout(() => {
        setIsVisible(false);
      }, 10000);
    }
  }, [generateRandomPosition, isRobotActive]);

  // Listen for robot activation/deactivation events
  useEffect(() => {
    const handleRobotActivated = () => {
      setIsRobotActive(true);
      setIsVisible(false); // Hide button when robot is active
    };
    
    const handleRobotDeactivated = () => {
      setIsRobotActive(false);
    };

    window.addEventListener('robotActivated', handleRobotActivated);
    window.addEventListener('robotDeactivated', handleRobotDeactivated);

    return () => {
      window.removeEventListener('robotActivated', handleRobotActivated);
      window.removeEventListener('robotDeactivated', handleRobotDeactivated);
    };
  }, []);

  // Set up interval to show button every 2 minutes
  useEffect(() => {
    // Show button immediately on mount (after a short delay)
    const initialTimeout = setTimeout(() => {
      showButton();
    }, 10000); // Show first button after 10 seconds

    // Then show every 2 minutes
    const interval = setInterval(() => {
      showButton();
    }, 2 * 60 * 1000); // 2 minutes

    return () => {
      clearTimeout(initialTimeout);
      clearInterval(interval);
    };
  }, [showButton]);

  // Handle window resize to reposition button if needed
  useEffect(() => {
    const handleResize = () => {
      if (isVisible) {
        const newPosition = generateRandomPosition();
        setPosition(newPosition);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isVisible, generateRandomPosition]);

  if (!isVisible || isRobotActive) {
    return null;
  }

  return (
    <div
      className={`fixed z-[0] ${className}`} // Very low z-index to stay behind everything
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        transition: 'opacity 0.3s ease-in-out',
        pointerEvents: 'auto', // Ensure button is clickable
      }}
    >
      <Button
        onClick={handleSpeakToDaswos}
        variant="outline"
        size="icon"
        className="rounded-full bg-transparent border-0 shadow-lg h-16 w-16 p-0 overflow-hidden hover:scale-105 transition-transform duration-200 animate-pulse"
        title="Speak to Daswos"
        style={{
          pointerEvents: 'auto',
          boxShadow: '0 0 20px rgba(59, 130, 246, 0.5)' // Blue glow for visibility
        }}
      >
        <div className="relative w-full h-full rounded-full overflow-hidden bg-gray-700 dark:bg-gray-700 hover:bg-gray-600 dark:hover:bg-gray-600 transition-colors duration-200">
          <img
            src="/assets/robot/daswos_redesign_correct_logo_simple.png"
            alt="Daswos Robot"
            className="w-full h-full object-cover"
            style={{
              filter: 'brightness(1.1) contrast(1.1)',
              transform: 'scale(1.2)'
            }}
          />
          {/* Overlay for better visibility */}
          <div className="absolute inset-0 bg-black/10 rounded-full"></div>
        </div>
      </Button>
    </div>
  );
};

export default MovingDaswosButton;

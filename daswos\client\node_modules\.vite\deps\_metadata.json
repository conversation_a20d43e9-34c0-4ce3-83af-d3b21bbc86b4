{"hash": "bce45362", "configHash": "3aed398e", "lockfileHash": "a9e10829", "browserHash": "8ad47008", "optimized": {"react": {"src": "../../../../node_modules/react/index.js", "file": "react.js", "fileHash": "6079be53", "needsInterop": true}, "react-dom": {"src": "../../../../node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "32ac53bf", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../../../node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "e7848914", "needsInterop": true}, "react/jsx-runtime": {"src": "../../../../node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "7c4f503e", "needsInterop": true}, "@hookform/resolvers/zod": {"src": "../../../../node_modules/@hookform/resolvers/zod/dist/zod.mjs", "file": "@hookform_resolvers_zod.js", "fileHash": "dd552ac0", "needsInterop": false}, "@radix-ui/react-alert-dialog": {"src": "../../../../node_modules/@radix-ui/react-alert-dialog/dist/index.mjs", "file": "@radix-ui_react-alert-dialog.js", "fileHash": "3d740480", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../../../node_modules/@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "7a63dcf4", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../../../node_modules/@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "b2a54b3e", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../../../node_modules/@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "7c090d6d", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../../../node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "d29bb619", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../../../node_modules/@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "6f8e674f", "needsInterop": false}, "@radix-ui/react-popover": {"src": "../../../../node_modules/@radix-ui/react-popover/dist/index.mjs", "file": "@radix-ui_react-popover.js", "fileHash": "d42825a3", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../../../node_modules/@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "6b5ae583", "needsInterop": false}, "@radix-ui/react-radio-group": {"src": "../../../../node_modules/@radix-ui/react-radio-group/dist/index.mjs", "file": "@radix-ui_react-radio-group.js", "fileHash": "72a22082", "needsInterop": false}, "@radix-ui/react-scroll-area": {"src": "../../../../node_modules/@radix-ui/react-scroll-area/dist/index.mjs", "file": "@radix-ui_react-scroll-area.js", "fileHash": "891bbe0d", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../../../node_modules/@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "82f016a5", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../../../node_modules/@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "cb429e50", "needsInterop": false}, "@radix-ui/react-slider": {"src": "../../../../node_modules/@radix-ui/react-slider/dist/index.mjs", "file": "@radix-ui_react-slider.js", "fileHash": "7c6e0e9d", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../../../node_modules/@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "f25e3149", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../../../node_modules/@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "372424f2", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../../../node_modules/@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "af39d5f4", "needsInterop": false}, "@stripe/react-stripe-js": {"src": "../../../../node_modules/@stripe/react-stripe-js/dist/react-stripe.esm.mjs", "file": "@stripe_react-stripe-js.js", "fileHash": "3f722f33", "needsInterop": false}, "@stripe/stripe-js": {"src": "../../../../node_modules/@stripe/stripe-js/lib/index.mjs", "file": "@stripe_stripe-js.js", "fileHash": "af571fbd", "needsInterop": false}, "@tanstack/react-query": {"src": "../../../../node_modules/@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "2635b56b", "needsInterop": false}, "class-variance-authority": {"src": "../../../../node_modules/class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "0e161159", "needsInterop": false}, "clsx": {"src": "../../../../node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "d55f1fa8", "needsInterop": false}, "framer-motion": {"src": "../../../../node_modules/framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "7d6c109e", "needsInterop": false}, "lucide-react": {"src": "../../../../node_modules/lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "013d7a60", "needsInterop": false}, "react-beautiful-dnd": {"src": "../../../../node_modules/react-beautiful-dnd/dist/react-beautiful-dnd.esm.js", "file": "react-beautiful-dnd.js", "fileHash": "53f277d5", "needsInterop": false}, "react-dom/client": {"src": "../../../../node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "4655e122", "needsInterop": true}, "react-helmet": {"src": "../../../../node_modules/react-helmet/es/Helmet.js", "file": "react-helmet.js", "fileHash": "5f2428e1", "needsInterop": false}, "react-hook-form": {"src": "../../../../node_modules/react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "ac02cb7e", "needsInterop": false}, "react-p5": {"src": "../../../../node_modules/react-p5/build/index.js", "file": "react-p5.js", "fileHash": "8f68e138", "needsInterop": true}, "tailwind-merge": {"src": "../../../../node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "f9fdeac2", "needsInterop": false}, "wouter": {"src": "../../../../node_modules/wouter/esm/index.js", "file": "wouter.js", "fileHash": "9500a383", "needsInterop": false}, "zod": {"src": "../../../../node_modules/zod/lib/index.mjs", "file": "zod.js", "fileHash": "e7a74e0d", "needsInterop": false}}, "chunks": {"chunk-N6SW53FP": {"file": "chunk-N6SW53FP.js"}, "chunk-2KCZN3IN": {"file": "chunk-2KCZN3IN.js"}, "chunk-YYAY3AAE": {"file": "chunk-YYAY3AAE.js"}, "chunk-NI3JHNNM": {"file": "chunk-NI3JHNNM.js"}, "chunk-UQF5EARV": {"file": "chunk-UQF5EARV.js"}, "chunk-HBKJJWUT": {"file": "chunk-HBKJJWUT.js"}, "chunk-XTAUCOQC": {"file": "chunk-XTAUCOQC.js"}, "chunk-MA223RL2": {"file": "chunk-MA223RL2.js"}, "chunk-QQGRI3EU": {"file": "chunk-QQGRI3EU.js"}, "chunk-AJUVNTZZ": {"file": "chunk-AJUVNTZZ.js"}, "chunk-T7YHF465": {"file": "chunk-T7YHF465.js"}, "chunk-RUTJ3L2W": {"file": "chunk-RUTJ3L2W.js"}, "chunk-Z5HXLDJI": {"file": "chunk-Z5HXLDJI.js"}, "chunk-JI27VNIC": {"file": "chunk-JI27VNIC.js"}, "chunk-QKI3G5ML": {"file": "chunk-QKI3G5ML.js"}, "chunk-PVOMQG6Z": {"file": "chunk-PVOMQG6Z.js"}, "chunk-UB6ZE4X2": {"file": "chunk-UB6ZE4X2.js"}, "chunk-MWRLGAH7": {"file": "chunk-MWRLGAH7.js"}, "chunk-FNUHTYUW": {"file": "chunk-FNUHTYUW.js"}, "chunk-LN47T4UX": {"file": "chunk-LN47T4UX.js"}, "chunk-F3OYNICX": {"file": "chunk-F3OYNICX.js"}, "chunk-PHDAYJMQ": {"file": "chunk-PHDAYJMQ.js"}, "chunk-NBSTMCL3": {"file": "chunk-NBSTMCL3.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}
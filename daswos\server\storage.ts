// Server-side storage implementation
import { db } from './db';
import { eq, sql, and, or, ilike, desc } from 'drizzle-orm';
import { users, dasWosCoinsTransactions, walletCards, products, cartItems } from '../shared/schema1';

// Simple server storage implementation with just the methods we need
export const storage = {
  // Get user's DasWos Coins balance
  async getUserDasWosCoins(userId: number): Promise<number> {
    try {
      console.log(`💰 Getting DasWos Coins balance for user ${userId}`);

      // Get user to check if they exist
      const [user] = await db.select().from(users).where(eq(users.id, userId));
      if (!user) {
        console.log(`❌ User with ID ${userId} not found`);
        throw new Error(`User with ID ${userId} not found`);
      }

      // Get the user's coin balance from transactions
      // Sum all transactions (add transactions as positive, spend transactions as negative)
      const transactions = await db
        .select({
          totalCoins: sql`COALESCE(SUM(CASE
            WHEN transaction_type = 'purchase' OR transaction_type = 'reward' OR transaction_type = 'refund' OR transaction_type = 'admin' THEN amount
            WHEN transaction_type = 'spend' THEN -amount
            ELSE 0
          END), 0)::integer`
        })
        .from(dasWosCoinsTransactions)
        .where(eq(dasWosCoinsTransactions.userId, userId));

      const balance = transactions[0]?.totalCoins as number || 0;
      console.log(`💰 User ${userId} DasWos Coins balance: ${balance}`);
      return balance;
    } catch (error) {
      console.error(`❌ Error getting DasWos Coins balance for user ${userId}:`, error);
      return 0;
    }
  },

  // Add DasWos Coins to user account
  async addDasWosCoins(userId: number, amount: number, type: string, description: string, metadata?: any, wallet_id?: string): Promise<boolean> {
    try {
      console.log(`💰 Adding ${amount} DasWos Coins to user ${userId} (type: ${type})`);

      // Validate that amount is positive for adding coins
      if (amount <= 0) {
        throw new Error('Amount must be positive when adding coins');
      }

      // Validate type is valid for adding coins
      const validTypes = ['purchase', 'reward', 'refund', 'admin'];
      if (!validTypes.includes(type)) {
        throw new Error(`Invalid type for adding coins: ${type}. Must be one of: ${validTypes.join(', ')}`);
      }

      // Add transaction record
      await db.insert(dasWosCoinsTransactions).values({
        userId,
        amount,
        transactionType: type, // Use transactionType field
        description,
        status: 'completed',
        metadata: metadata ? JSON.stringify(metadata) : null,
        walletId: wallet_id || null,
        createdAt: new Date()
      });

      // If this is a reward or admin gift, also update the total_won_daswos_coins column
      if (type === 'reward' || type === 'admin') {
        await db
          .update(users)
          .set({
            totalWonDasWosCoins: sql`total_won_daswos_coins + ${amount}`,
            updatedAt: new Date()
          })
          .where(eq(users.id, userId));

        console.log(`🎁 Updated total_won_daswos_coins for user ${userId} by ${amount}`);
      }

      console.log(`✅ Successfully added ${amount} DasWos Coins to user ${userId}`);
      return true;
    } catch (error) {
      console.error(`❌ Error adding DasWos Coins to user ${userId}:`, error);
      throw error;
    }
  },

  // Spend DasWos Coins from user account
  async spendDasWosCoins(userId: number, amount: number, description: string, metadata?: any, wallet_id?: string): Promise<boolean> {
    try {
      console.log(`💰 User ${userId} spending ${amount} DasWos Coins`);

      // Validate that amount is positive for spending
      if (amount <= 0) {
        throw new Error('Amount must be positive when spending coins');
      }

      // Check if user has enough coins
      const balance = await this.getUserDasWosCoins(userId);
      if (balance < amount) {
        throw new Error(`Insufficient balance. User has ${balance} coins, attempting to spend ${amount}`);
      }

      // Add spend transaction
      await db.insert(dasWosCoinsTransactions).values({
        userId,
        amount, // Store as positive value, but will be subtracted in balance calculation
        transactionType: 'spend',
        description,
        status: 'completed',
        metadata: metadata ? JSON.stringify(metadata) : null,
        walletId: wallet_id || null,
        createdAt: new Date()
      });

      console.log(`✅ Successfully spent ${amount} DasWos Coins for user ${userId}`);
      return true;
    } catch (error) {
      console.error(`❌ Error spending DasWos Coins for user ${userId}:`, error);
      throw error;
    }
  },

  // Check user wallet access
  async checkUserWalletAccess(userId: number, walletId?: string): Promise<{
    hasAccess: boolean;
    userWalletId?: string;
    requiresWalletLink?: boolean;
  }> {
    try {
      console.log(`🔐 Checking wallet access for user ${userId}, wallet: ${walletId}`);

      // Get user's primary wallet ID
      const [user] = await db.select().from(users).where(eq(users.id, userId));
      if (!user) {
        console.log(`❌ User ${userId} not found for wallet access check`);
        return {
          hasAccess: false,
          requiresWalletLink: true
        };
      }

      const userWalletId = user.walletId;
      console.log(`🔐 User ${userId} linked wallet: ${userWalletId}`);

      // If no wallet ID provided, check if user has a linked wallet
      if (!walletId) {
        const result = {
          hasAccess: !!userWalletId,
          userWalletId,
          requiresWalletLink: !userWalletId
        };
        console.log(`🔐 No wallet ID provided, result:`, result);
        return result;
      }

      // If user has no linked wallet, deny access
      if (!userWalletId) {
        console.log(`🔐 User ${userId} has no linked wallet, denying access`);
        return {
          hasAccess: false,
          requiresWalletLink: true
        };
      }

      // Check if provided wallet matches user's linked wallet
      const hasAccess = userWalletId === walletId;
      const result = {
        hasAccess,
        userWalletId,
        requiresWalletLink: false
      };

      console.log(`🔐 Wallet access check result for user ${userId}:`, result);
      return result;
    } catch (error) {
      console.error('❌ Error checking user wallet access:', error);
      return {
        hasAccess: false,
        requiresWalletLink: true
      };
    }
  },

  // Test database connection
  async testConnection(): Promise<boolean> {
    try {
      await db.select().from(users).limit(1);
      return true;
    } catch (error) {
      console.error('Database connection test failed:', error);
      return false;
    }
  },

  // Get products (proper database implementation)
  async getProducts(sphere: string, query?: string): Promise<any[]> {
    try {
      console.log(`🛍️ Getting products for sphere: ${sphere}, query: ${query}`);

      // Build the base query
      let conditions = [];

      // Filter by sphere (SafeSphere vs OpenSphere)
      if (sphere === 'safesphere') {
        // SafeSphere: Secure filtering - trust score >= 60 OR seller verified (NO null trust scores allowed)
        conditions.push(
          or(
            sql`${products.trustScore} >= 60`,
            eq(products.sellerVerified, true)
          )
        );
      } else if (sphere === 'bulkbuy-safe') {
        // BulkBuy with SafeSphere filter - bulk buy eligible AND safer products
        conditions.push(
          and(
            eq(products.isBulkBuy, true),
            or(
              sql`${products.trustScore} >= 60`,
              eq(products.sellerVerified, true)
            )
          )
        );
      } else if (sphere === 'bulkbuy-open') {
        // BulkBuy with OpenSphere - just bulk buy eligible products
        conditions.push(eq(products.isBulkBuy, true));
      }
      // For opensphere or any other sphere, show all active products

      // Add status filter (only active products)
      conditions.push(eq(products.status, 'active'));

      // Add search query filter if provided
      if (query && query.trim()) {
        const searchTerm = `%${query.toLowerCase()}%`;
        conditions.push(
          or(
            ilike(products.title, searchTerm),
            ilike(products.description, searchTerm),
            sql`array_to_string(${products.tags}, ' ') ILIKE ${searchTerm}`,
            ilike(products.sellerName, searchTerm)
          )
        );
      }

      // Build the final query
      let dbQuery = db.select().from(products);

      if (conditions.length > 0) {
        dbQuery = dbQuery.where(and(...conditions));
      }

      // Order by trust score (highest first), then by creation date (newest first)
      dbQuery = dbQuery.orderBy(desc(products.trustScore), desc(products.createdAt));

      // Execute the query
      const results = await dbQuery;

      console.log(`🛍️ Found ${results.length} products for sphere: ${sphere}, query: "${query}"`);

      // Log sample results for debugging
      if (results.length > 0) {
        console.log('🛍️ Sample products:', results.slice(0, 3).map(p => ({
          id: p.id,
          title: p.title,
          trustScore: p.trustScore,
          sellerVerified: p.sellerVerified,
          sphere: sphere
        })));
      }

      return results;
    } catch (error) {
      console.error('❌ Error getting products:', error);
      return [];
    }
  },

  // Get products by category
  async getProductsByCategory(categoryName: string): Promise<any[]> {
    try {
      console.log(`🛍️ Getting products by category: ${categoryName}`);

      // For now, just return all products since we need to implement category filtering
      // This can be enhanced later with proper category joins
      const results = await db.select().from(products)
        .where(eq(products.status, 'active'))
        .orderBy(desc(products.trustScore), desc(products.createdAt));

      console.log(`🛍️ Found ${results.length} products for category: ${categoryName}`);
      return results;
    } catch (error) {
      console.error('❌ Error getting products by category:', error);
      return [];
    }
  },

  // Get product by ID
  async getProductById(id: number): Promise<any | undefined> {
    try {
      console.log(`🛍️ Getting product by ID: ${id}`);

      const [product] = await db.select().from(products).where(eq(products.id, id));

      if (product) {
        console.log(`🛍️ Found product: ${product.title} (ID: ${product.id})`);
      } else {
        console.log(`🛍️ Product not found with ID: ${id}`);
      }

      return product;
    } catch (error) {
      console.error(`❌ Error getting product by ID ${id}:`, error);
      return undefined;
    }
  },

  // Create product
  async createProduct(productData: any): Promise<any> {
    try {
      console.log(`🛍️ Creating product: ${productData.title}`);

      const [newProduct] = await db.insert(products).values({
        title: productData.title,
        description: productData.description,
        price: productData.price,
        imageUrl: productData.imageUrl,
        sellerId: productData.sellerId,
        sellerName: productData.sellerName,
        sellerVerified: productData.sellerVerified,
        sellerType: productData.sellerType,
        trustScore: productData.trustScore,
        identityVerified: productData.identityVerified,
        identityVerificationStatus: productData.identityVerificationStatus,
        tags: productData.tags,
        shipping: productData.shipping,
        originalPrice: productData.originalPrice,
        discount: productData.discount,
        verifiedSince: productData.verifiedSince,
        warning: productData.warning,
        isBulkBuy: productData.isBulkBuy,
        bulkMinimumQuantity: productData.bulkMinimumQuantity,
        bulkDiscountRate: productData.bulkDiscountRate,
        imageDescription: productData.imageDescription,
        categoryId: productData.categoryId,
        aiAttributes: productData.aiAttributes,
        searchVector: productData.searchVector,
        status: productData.status,
        quantity: productData.quantity,
        soldQuantity: productData.soldQuantity,
        createdAt: new Date(),
        updatedAt: new Date()
      }).returning();

      console.log(`✅ Created product: ${newProduct.title} (ID: ${newProduct.id})`);
      return newProduct;
    } catch (error) {
      console.error(`❌ Error creating product:`, error);
      throw error;
    }
  },

  // Update user seller status
  async updateUserSellerStatus(userId: number, isSeller: boolean): Promise<boolean> {
    try {
      console.log(`👤 Updating seller status for user ${userId}: ${isSeller}`);

      await db
        .update(users)
        .set({
          isSeller: isSeller,
          updatedAt: new Date()
        })
        .where(eq(users.id, userId));

      console.log(`✅ Updated seller status for user ${userId} to ${isSeller}`);
      return true;
    } catch (error) {
      console.error(`❌ Error updating seller status for user ${userId}:`, error);
      return false;
    }
  },

  // Get products by seller ID
  async getProductsBySellerId(sellerId: number): Promise<any[]> {
    try {
      console.log(`🛍️ Getting products by seller ID: ${sellerId}`);

      const results = await db.select().from(products)
        .where(eq(products.sellerId, sellerId))
        .orderBy(desc(products.createdAt));

      console.log(`🛍️ Found ${results.length} products for seller ${sellerId}`);
      return results;
    } catch (error) {
      console.error(`❌ Error getting products by seller ID ${sellerId}:`, error);
      return [];
    }
  },

  // Get purchases by seller ID (for trust score calculation)
  async getPurchasesBySellerId(sellerId: number): Promise<any[]> {
    try {
      console.log(`💰 Getting purchases by seller ID: ${sellerId}`);

      // For now, return empty array since we don't have purchases implemented yet
      // This can be enhanced later when the purchases system is fully implemented
      return [];
    } catch (error) {
      console.error(`❌ Error getting purchases by seller ID ${sellerId}:`, error);
      return [];
    }
  },

  // Get user by username
  async getUserByUsername(username: string): Promise<any | undefined> {
    try {
      console.log(`👤 Getting user by username: ${username}`);

      // Get user by username (case-insensitive)
      const [user] = await db.select().from(users).where(sql`LOWER(${users.username}) = LOWER(${username})`);

      if (user) {
        console.log(`👤 Found user: ${user.username} (ID: ${user.id})`);
      } else {
        console.log(`👤 User not found: ${username}`);
      }

      return user;
    } catch (error) {
      console.error(`❌ Error getting user by username ${username}:`, error);
      return undefined;
    }
  },

  // Get user by ID
  async getUserById(userId: number): Promise<any | undefined> {
    try {
      console.log(`👤 Getting user by ID: ${userId}`);

      const [user] = await db.select().from(users).where(eq(users.id, userId));

      if (user) {
        console.log(`👤 Found user: ${user.username} (ID: ${user.id})`);
      } else {
        console.log(`👤 User not found with ID: ${userId}`);
      }

      return user;
    } catch (error) {
      console.error(`❌ Error getting user by ID ${userId}:`, error);
      return undefined;
    }
  },

  // Get DasWos Coins transactions for a user
  async getDasWosCoinsTransactions(userId: number): Promise<any[]> {
    try {
      console.log(`💰 Getting DasWos Coins transactions for user ${userId}`);

      const transactions = await db
        .select()
        .from(dasWosCoinsTransactions)
        .where(eq(dasWosCoinsTransactions.userId, userId))
        .orderBy(sql`${dasWosCoinsTransactions.createdAt} DESC`);

      console.log(`💰 Found ${transactions.length} transactions for user ${userId}`);
      return transactions;
    } catch (error) {
      console.error(`❌ Error getting transactions for user ${userId}:`, error);
      return [];
    }
  },

  // Get user by email
  async getUserByEmail(email: string): Promise<any | undefined> {
    try {
      console.log(`📧 Getting user by email: ${email}`);

      // Get user by email (case-insensitive)
      const [user] = await db.select().from(users).where(sql`LOWER(${users.email}) = LOWER(${email})`);

      if (user) {
        console.log(`📧 Found user: ${user.username} (${user.email})`);
      } else {
        console.log(`📧 User not found with email: ${email}`);
      }

      return user;
    } catch (error) {
      console.error(`❌ Error getting user by email ${email}:`, error);
      return undefined;
    }
  },

  // Create new user
  async createUser(userData: any): Promise<any> {
    try {
      console.log(`👤 Creating new user: ${userData.username} (${userData.email})`);

      // Generate a unique wallet ID for the new user
      const walletId = `wallet_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      console.log(`🔑 Generated wallet ID: ${walletId}`);

      const [newUser] = await db.insert(users).values({
        username: userData.username,
        email: userData.email,
        password: userData.password,
        fullName: userData.fullName || userData.username,
        walletId: walletId, // Add the generated wallet ID
        createdAt: new Date(),
        updatedAt: new Date(),
        isSeller: false,
        isAdmin: false,
        avatar: '',
        hasSubscription: false,
        subscriptionType: 'limited',
        subscriptionExpiresAt: null,
        isFamilyOwner: false,
        familyOwnerId: null,
        isChild: false,
        isVerified: true,
        verificationToken: null,
        resetPasswordToken: null,
        resetPasswordExpires: null,
        lastLoginAt: new Date(),
        dasWosCoins: 0
      }).returning();

      console.log(`✅ Created user: ${newUser.username} (ID: ${newUser.id}) with wallet ID: ${newUser.walletId}`);
      return newUser;
    } catch (error) {
      console.error(`❌ Error creating user:`, error);
      throw error;
    }
  },

  // Assign wallet ID to existing user (for users created before wallet ID generation)
  async assignWalletIdToUser(userId: number): Promise<string> {
    try {
      console.log(`🔑 Assigning wallet ID to user ${userId}`);

      // Generate a unique wallet ID
      const walletId = `wallet_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Update the user with the new wallet ID
      const [updatedUser] = await db
        .update(users)
        .set({
          walletId: walletId,
          updatedAt: new Date()
        })
        .where(eq(users.id, userId))
        .returning();

      console.log(`✅ Assigned wallet ID ${walletId} to user ${userId}`);
      return walletId;
    } catch (error) {
      console.error(`❌ Error assigning wallet ID to user ${userId}:`, error);
      throw error;
    }
  },

  // Enhanced getUserById that ensures wallet ID exists
  async getUserByIdWithWallet(userId: number): Promise<any | undefined> {
    try {
      console.log(`👤 Getting user by ID with wallet check: ${userId}`);

      const [user] = await db.select().from(users).where(eq(users.id, userId));

      if (user) {
        // If user doesn't have a wallet ID, assign one
        if (!user.walletId) {
          console.log(`🔑 User ${userId} missing wallet ID, assigning one...`);
          const walletId = await this.assignWalletIdToUser(userId);
          user.walletId = walletId;
        }

        console.log(`👤 Found user: ${user.username} (ID: ${user.id}) with wallet: ${user.walletId}`);
      } else {
        console.log(`👤 User not found with ID: ${userId}`);
      }

      return user;
    } catch (error) {
      console.error(`❌ Error getting user by ID with wallet ${userId}:`, error);
      return undefined;
    }
  },

  // Get user sessions (placeholder for auth middleware)
  async getUserSessions(userId: number | null): Promise<any[]> {
    try {
      console.log(`🔐 Getting user sessions for user: ${userId}`);
      // For now, return empty array since we're using passport sessions
      // This method is called by the auth middleware but not critical for wallet creation
      return [];
    } catch (error) {
      console.error(`❌ Error getting user sessions:`, error);
      return [];
    }
  },

  // Get user (alias for getUserById for compatibility)
  async getUser(userId: number): Promise<any | undefined> {
    return this.getUserById(userId);
  },

  // Update user subscription (placeholder)
  async updateUserSubscription(userId: number, subscriptionType: string, expiresAt: number | null): Promise<void> {
    try {
      console.log(`📋 Updating subscription for user ${userId}: ${subscriptionType}`);

      await db
        .update(users)
        .set({
          subscriptionType: subscriptionType,
          hasSubscription: true,
          subscriptionExpiresAt: expiresAt ? new Date(expiresAt) : null,
          updatedAt: new Date()
        })
        .where(eq(users.id, userId));

      console.log(`✅ Updated subscription for user ${userId}`);
    } catch (error) {
      console.error(`❌ Error updating subscription for user ${userId}:`, error);
      throw error;
    }
  },

  // Create user session (placeholder for auth)
  async createUserSession(sessionData: any): Promise<any> {
    try {
      console.log(`🔐 Creating user session for user ${sessionData.userId}`);

      // For now, return a mock session since we're using passport sessions
      // This method is called by the auth system but not critical for wallet creation
      return {
        id: Date.now(),
        userId: sessionData.userId,
        sessionToken: sessionData.sessionToken,
        expiresAt: sessionData.expiresAt,
        createdAt: new Date()
      };
    } catch (error) {
      console.error(`❌ Error creating user session:`, error);
      throw error;
    }
  },

  // Session store (placeholder for express-session)
  sessionStore: null as any,

  // SafeSphere operations
  async getSafeSphereStatus(userId: number): Promise<boolean> {
    try {
      console.log(`🛡️ Getting SafeSphere status for user ${userId}`);

      const [user] = await db.select().from(users).where(eq(users.id, userId));
      if (!user) {
        console.log(`❌ User ${userId} not found for SafeSphere status`);
        return false;
      }

      const isActive = user.safeSphereActive || false;
      console.log(`🛡️ SafeSphere status for user ${userId}: ${isActive}`);
      return isActive;
    } catch (error) {
      console.error(`❌ Error getting SafeSphere status for user ${userId}:`, error);
      return false;
    }
  },

  async updateSafeSphereStatus(userId: number, active: boolean): Promise<boolean> {
    try {
      console.log(`🛡️ Updating SafeSphere status for user ${userId}: ${active}`);

      await db
        .update(users)
        .set({
          safeSphereActive: active,
          updatedAt: new Date()
        })
        .where(eq(users.id, userId));

      console.log(`✅ Updated SafeSphere status for user ${userId} to ${active}`);
      return true;
    } catch (error) {
      console.error(`❌ Error updating SafeSphere status for user ${userId}:`, error);
      return false;
    }
  },

  // SuperSafe operations
  async getSuperSafeStatus(userId: number): Promise<{enabled: boolean, settings: any}> {
    try {
      console.log(`🔒 Getting SuperSafe status for user ${userId}`);

      const [user] = await db.select().from(users).where(eq(users.id, userId));
      if (!user) {
        console.log(`❌ User ${userId} not found for SuperSafe status`);
        return {
          enabled: false,
          settings: {
            blockGambling: true,
            blockAdultContent: true,
            blockOpenSphere: false
          }
        };
      }

      const result = {
        enabled: user.superSafeMode || false,
        settings: user.superSafeSettings || {
          blockGambling: true,
          blockAdultContent: true,
          blockOpenSphere: false
        }
      };

      console.log(`🔒 SuperSafe status for user ${userId}:`, result);
      return result;
    } catch (error) {
      console.error(`❌ Error getting SuperSafe status for user ${userId}:`, error);
      return {
        enabled: false,
        settings: {
          blockGambling: true,
          blockAdultContent: true,
          blockOpenSphere: false
        }
      };
    }
  },

  async updateSuperSafeStatus(userId: number, enabled: boolean, settings?: any): Promise<boolean> {
    try {
      console.log(`🔒 Updating SuperSafe status for user ${userId}: enabled=${enabled}`, settings);

      const [user] = await db.select().from(users).where(eq(users.id, userId));
      if (!user) {
        console.log(`❌ User ${userId} not found for SuperSafe update`);
        return false;
      }

      // If settings not provided, keep existing settings or use defaults
      const updatedSettings = settings || user.superSafeSettings || {
        blockGambling: true,
        blockAdultContent: true,
        blockOpenSphere: false
      };

      await db
        .update(users)
        .set({
          superSafeMode: enabled,
          superSafeSettings: updatedSettings,
          updatedAt: new Date()
        })
        .where(eq(users.id, userId));

      console.log(`✅ Updated SuperSafe status for user ${userId} to enabled=${enabled}`);
      return true;
    } catch (error) {
      console.error(`❌ Error updating SuperSafe status for user ${userId}:`, error);
      return false;
    }
  },

  // App settings (placeholder for compatibility)
  async getAppSettings(key: string): Promise<any> {
    try {
      console.log(`⚙️ Getting app setting: ${key}`);

      // For now, return default values for known settings
      switch (key) {
        case 'paidFeaturesDisabled':
          return false; // Paid features are enabled by default
        default:
          return null;
      }
    } catch (error) {
      console.error(`❌ Error getting app setting ${key}:`, error);
      return null;
    }
  },

  // Sync user's total DasWos coins balance from card balances
  async syncUserTotalBalance(userId: number): Promise<number> {
    try {
      console.log(`🔄 Syncing total balance for user ${userId}`);

      // Get all cards for this user
      const allUserCards = await db
        .select({ balance: walletCards.balance })
        .from(walletCards)
        .where(eq(walletCards.userId, userId));

      // Calculate total balance
      const totalBalance = allUserCards.reduce((sum, card) => sum + card.balance, 0);
      console.log(`💰 Calculated total balance for user ${userId}: ${totalBalance} (from ${allUserCards.length} cards)`);

      // Update user's total balance
      await db
        .update(users)
        .set({
          dasWosCoinsBalance: totalBalance,
          updatedAt: new Date()
        })
        .where(eq(users.id, userId));

      console.log(`✅ Synced user ${userId} total balance to ${totalBalance}`);
      return totalBalance;
    } catch (error) {
      console.error(`❌ Error syncing total balance for user ${userId}:`, error);
      throw error;
    }
  },

  // Sync all users' total balances (admin function)
  async syncAllUserTotalBalances(): Promise<void> {
    try {
      console.log(`🔄 Syncing total balances for all users`);

      // Get all users who have cards
      const usersWithCards = await db
        .selectDistinct({ userId: walletCards.userId })
        .from(walletCards);

      console.log(`Found ${usersWithCards.length} users with wallet cards`);

      for (const { userId } of usersWithCards) {
        await this.syncUserTotalBalance(userId);
      }

      console.log(`✅ Synced total balances for all ${usersWithCards.length} users`);
    } catch (error) {
      console.error(`❌ Error syncing all user total balances:`, error);
      throw error;
    }
  },

  // Cart management methods
  async addCartItem(item: any): Promise<any> {
    try {
      console.log('🛒 Adding item to cart:', item);

      // Check if item already exists for this user
      const existingItems = await db.select()
        .from(cartItems)
        .where(
          and(
            eq(cartItems.userId, item.userId),
            eq(cartItems.productId, item.productId)
          )
        );

      if (existingItems.length > 0) {
        // Item already exists, update quantity
        const existingItem = existingItems[0];
        const newQuantity = existingItem.quantity + (item.quantity || 1);

        const [updatedItem] = await db.update(cartItems)
          .set({
            quantity: newQuantity,
            updatedAt: new Date(),
            ...(item.source ? { source: item.source } : {})
          })
          .where(eq(cartItems.id, existingItem.id))
          .returning();

        console.log('🛒 Updated existing cart item:', updatedItem);
        return updatedItem;
      } else {
        // Item doesn't exist, insert new
        const newItem = {
          ...item,
          quantity: item.quantity || 1,
          addedAt: new Date(),
          updatedAt: null,
          source: item.source || 'manual'
        };

        const [createdItem] = await db.insert(cartItems).values(newItem).returning();
        console.log('🛒 Created new cart item:', createdItem);
        return createdItem;
      }
    } catch (error) {
      console.error('❌ Error adding item to cart:', error);
      throw error;
    }
  },

  async updateCartItemQuantity(itemId: number, quantity: number): Promise<any> {
    try {
      console.log(`🛒 Updating cart item ${itemId} quantity to ${quantity}`);

      const [updatedItem] = await db.update(cartItems)
        .set({
          quantity: quantity,
          updatedAt: new Date()
        })
        .where(eq(cartItems.id, itemId))
        .returning();

      console.log('🛒 Updated cart item:', updatedItem);
      return updatedItem;
    } catch (error) {
      console.error(`❌ Error updating cart item ${itemId}:`, error);
      throw error;
    }
  },

  async removeCartItem(itemId: number): Promise<void> {
    try {
      console.log(`🛒 Removing cart item ${itemId}`);

      await db.delete(cartItems)
        .where(eq(cartItems.id, itemId));

      console.log(`✅ Removed cart item ${itemId}`);
    } catch (error) {
      console.error(`❌ Error removing cart item ${itemId}:`, error);
      throw error;
    }
  },

  async clearUserCart(userId: number): Promise<void> {
    try {
      console.log(`🛒 Clearing cart for user ${userId}`);

      await db.delete(cartItems)
        .where(eq(cartItems.userId, userId));

      console.log(`✅ Cleared cart for user ${userId}`);
    } catch (error) {
      console.error(`❌ Error clearing cart for user ${userId}:`, error);
      throw error;
    }
  }
};

import React, { useState, useRef, useEffect } from 'react';
import { useLocation } from 'wouter';
import { Search, Sun, Moon, X, ShoppingCart, RefreshCw, Expand, Minimize, Eraser, Pause, Sparkles } from 'lucide-react';
import { useTheme } from '@/providers/theme-provider';
import <PERSON><PERSON><PERSON><PERSON>ogo from '@/components/daswos-logo';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import AnimatedTrustText from '@/components/animated-trust-text';
import StatusLabel from '@/components/status-label';
import ShoppingResults from '@/components/shopping-results';
import InformationResults from '@/components/information-results';
import SafeCardIndicator from '@/components/safe-card-indicator';
import { useSafeSphereContext } from '@/contexts/safe-sphere-context';
import EarnPointsToggle from '@/components/earn-points-toggle';
import { useEarnPoints } from '@/contexts/earn-points-context';
import { Product } from '@shared/schema';

interface SearchInterfaceProps {
  onSearch: (query: string) => void;
  aiModeEnabled?: boolean; // Always true now, but kept for compatibility
  activeSphere?: 'safesphere' | 'opensphere';
  onSphereChange?: (sphere: 'safesphere' | 'opensphere') => void;
  superSafeActive?: boolean;
  onToggleSuperSafe?: (active: boolean) => void;
  className?: string;
  showResults?: boolean;
  selectedResultType?: 'shopping' | 'information' | null;
  searchQuery?: string;
  onBuyCurrentProduct?: () => void;
  hasShoppingResults?: boolean;
  onCurrentProductChange?: (product: Product | null) => void;
  // Mode control props
  isAiConversationActive?: boolean;
  isAskingIfShopping?: boolean;
  searchType?: 'shopping' | 'information';
  onSearchTypeChange?: (type: 'shopping' | 'information') => void;
  onNewSearch?: () => void;
  aiResponse?: { text: string; hasAudio: boolean } | null;
  onSpeak?: (text: string) => void;
}

const SearchInterface: React.FC<SearchInterfaceProps> = ({
  onSearch,
  aiModeEnabled = true, // AI mode is always enabled now - Daswos AI is the default
  activeSphere = 'safesphere',
  onSphereChange,
  superSafeActive = false,
  onToggleSuperSafe,
  className = '',
  showResults = false,
  selectedResultType = null,
  searchQuery = '',
  onBuyCurrentProduct,
  hasShoppingResults = false,
  onCurrentProductChange,
  // Mode control props
  isAiConversationActive = false,
  isAskingIfShopping = false,
  searchType = 'shopping',
  onSearchTypeChange,
  onNewSearch,
  aiResponse = null,
  onSpeak
}) => {
  // Reference to the container
  const containerRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const { isEarnPointsEnabled, toggleEarnPoints } = useEarnPoints();
  const [currentQuery, setCurrentQuery] = useState(''); // Always start empty
  const [isQueryBeingEdited, setIsQueryBeingEdited] = useState(false);
  const [lastSubmittedQuery, setLastSubmittedQuery] = useState(searchQuery);
  const [searchContext, setSearchContext] = useState<string[]>([]); // Track search progression
  const [isInputFocused, setIsInputFocused] = useState(false);
  const [aiResponseText, setAiResponseText] = useState<string>('');
  const [voiceStatus, setVoiceStatus] = useState<string>('');
  // Load search history from localStorage on component mount
  const [searchHistory, setSearchHistory] = useState<string[]>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('searchHistory');
      return saved ? JSON.parse(saved) : [];
    }
    return [];
  });
  
  const [cachedResponses, setCachedResponses] = useState<Map<string, any>>(new Map());
  // Load search history contexts from localStorage on component mount
  const [searchHistoryContexts, setSearchHistoryContexts] = useState<string[][]>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('searchHistoryContexts');
      return saved ? JSON.parse(saved) : [];
    }
    return [];
  });
  const [aiResponsePaused, setAiResponsePaused] = useState<boolean>(false);
  const [aiResponseTimeoutId, setAiResponseTimeoutId] = useState<NodeJS.Timeout | null>(null);

  // State for search history navigation and response caching
  const [currentContextIndex, setCurrentContextIndex] = useState(-1);
  const [isAIBoxExpanded, setIsAIBoxExpanded] = useState(false);

  // Refine mode state
  const [refineMode, setRefineMode] = useState<boolean>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('refineMode');
      return saved ? JSON.parse(saved) : true; // Default to true (on)
    }
    return true;
  });

  // Toggle refine mode
  const toggleRefineMode = () => {
    const newRefineMode = !refineMode;
    setRefineMode(newRefineMode);
    localStorage.setItem('refineMode', JSON.stringify(newRefineMode));
    
    // Dispatch event to notify AI components of the change
    const event = new CustomEvent('aiInformationRefineMode', { 
      detail: { refineMode: newRefineMode } 
    });
    window.dispatchEvent(event);
  };

  // AI Information Box button states
  const [aiIsLoading, setAiIsLoading] = useState(false);
  const [aiHasContent, setAiHasContent] = useState(false);

  // Smart AI Assistant function
  const processSmartAIQuery = (query: string): { canAnswerBriefly: boolean; response?: string; shouldNavigateTo?: 'shopping' | 'information' } => {
    const lowerQuery = query.toLowerCase().trim();

    // Simple greetings and basic questions
    if (lowerQuery.match(/^(hi|hello|hey|good morning|good afternoon|good evening)$/)) {
      return { canAnswerBriefly: true, response: "Hello! How can I help you today?" };
    }

    if (lowerQuery.match(/^(how are you|how's it going)$/)) {
      return { canAnswerBriefly: true, response: "I'm doing great! Ready to help you find what you need." };
    }

    if (lowerQuery.match(/^(what can you do|what do you do|help)$/)) {
      return { canAnswerBriefly: true, response: "I can help you shop or find information. What would you like to do?" };
    }

    // Time-related questions
    if (lowerQuery.match(/^(what time is it|what's the time)$/)) {
      const now = new Date();
      const timeString = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      return { canAnswerBriefly: true, response: `It's ${timeString}` };
    }

    // Simple math questions - support both "what is X + Y" and direct "X + Y" formats
    const mathMatch = lowerQuery.match(/^(?:what is )?(\d+) (\+|\-|\*|\/|plus|minus|times|divided by) (\d+)$/);
    if (mathMatch) {
      const num1 = parseInt(mathMatch[1]);
      const operator = mathMatch[2];
      const num2 = parseInt(mathMatch[3]);
      let result;
      let displayOperator = operator;

      switch (operator) {
        case '+':
        case 'plus':
          result = num1 + num2;
          displayOperator = '+';
          break;
        case '-':
        case 'minus':
          result = num1 - num2;
          displayOperator = '-';
          break;
        case '*':
        case 'times':
          result = num1 * num2;
          displayOperator = '×';
          break;
        case '/':
        case 'divided by':
          result = num2 !== 0 ? num1 / num2 : 'undefined (division by zero)';
          displayOperator = '÷';
          break;
        default:
          result = 'error';
      }

      return { canAnswerBriefly: true, response: `${num1} ${displayOperator} ${num2} = ${result}` };
    }

    // Shopping-related queries (should go to shopping page)
    if (lowerQuery.match(/(buy|purchase|shop|store|price|cost|cheap|expensive|sale|discount)/)) {
      return { canAnswerBriefly: false, shouldNavigateTo: 'shopping' };
    }

    // Product/item queries (should go to shopping page)
    if (lowerQuery.match(/(shoes|shirt|pants|dress|jacket|bag|wallet|watch|phone|laptop|computer|headphones|camera|book|toy|game|furniture|chair|table|bed|sofa|car|bike|bicycle|product|item|thing|stuff|gear|equipment|clothes|clothing|electronics|gadget|device|tool|accessory|jewelry|makeup|skincare|perfume|cologne|food|snack|drink|beverage|supplement|vitamin|medicine|drug|pill|tablet|cream|lotion|shampoo|soap|toothbrush|toothpaste)/)) {
      return { canAnswerBriefly: false, shouldNavigateTo: 'shopping' };
    }

    // Information-related queries (should go to information page)
    if (lowerQuery.match(/(what is|who is|when is|where is|why|how|explain|tell me about|information about|learn about)/)) {
      return { canAnswerBriefly: false, shouldNavigateTo: 'information' };
    }

    // For any query that doesn't have a specific brief answer, navigate to appropriate page
    // Don't try to answer with clarifying questions - let the full search pages handle it

    // Default to information page for general queries
    return { canAnswerBriefly: false, shouldNavigateTo: 'information' };
  };

  // Update currentQuery when searchQuery prop changes
  useEffect(() => {
    if (searchQuery && searchQuery !== currentQuery) {
      setCurrentQuery(searchQuery);
      setLastSubmittedQuery(searchQuery);
      setIsQueryBeingEdited(false);
    }
  }, [searchQuery]);

  // Detect when user is editing the query
  const handleQueryChange = (value: string) => {
    setCurrentQuery(value);

    // If AI mode is enabled and we have shopping results, check if query has changed
    if (aiModeEnabled && hasShoppingResults && selectedResultType === 'shopping') {
      const isEditing = value.trim() !== lastSubmittedQuery.trim();
      setIsQueryBeingEdited(isEditing);

      // If user is editing, clear the current product to hide buy button
      if (isEditing && onCurrentProductChange) {
        onCurrentProductChange(null);
      }
    }
  };

  // Handle input focus - immediately switch to editing mode
  const handleInputFocus = () => {
    setIsInputFocused(true);

    // If AI mode is enabled and we have shopping results, switch to editing mode
    if (aiModeEnabled && hasShoppingResults && selectedResultType === 'shopping') {
      setIsQueryBeingEdited(true);

      // Clear the current product to hide buy button
      if (onCurrentProductChange) {
        onCurrentProductChange(null);
      }
    }
  };

  // Handle input blur - check if we should exit editing mode
  const handleInputBlur = () => {
    setIsInputFocused(false);

    // Only exit editing mode if the query matches the last submitted query
    if (aiModeEnabled && hasShoppingResults && selectedResultType === 'shopping') {
      const queryMatches = currentQuery.trim() === lastSubmittedQuery.trim();
      if (queryMatches) {
        setIsQueryBeingEdited(false);
        // Note: We don't restore the current product here as it will be set by the search results
      }
    }
  };

  const { theme, setTheme } = useTheme();
  const { toast } = useToast();
  const [, navigate] = useLocation();
  const { isLocked } = useSafeSphereContext();

  // Voice command handlers
  const handleVoiceSearchCommand = (query: string) => {
    // Remove trailing punctuation from voice search
    const cleanQuery = query.replace(/[.!?]+$/g, '').trim();

    // Clear voice status first so the search query shows in the input and becomes editable
    setVoiceStatus('');
    setCurrentQuery(cleanQuery);
    onSearch(cleanQuery);
    setLastSubmittedQuery(cleanQuery);
    setIsQueryBeingEdited(false);

    // Force clear voice status after a short delay to ensure input becomes editable
    setTimeout(() => {
      setVoiceStatus('');
    }, 100);

    toast({
      title: 'Voice Search',
      description: `"${cleanQuery}"`,
    });
  };

  const handleVoiceAutoShopCommand = () => {
    // Trigger AutoShop settings dialog by setting the query to a known AutoShop trigger
    const autoShopQuery = "let's autoshop";
    setCurrentQuery(autoShopQuery);
    onSearch(autoShopQuery);
    setLastSubmittedQuery(autoShopQuery);
    setIsQueryBeingEdited(false);

    toast({
      title: 'Voice Command Executed',
      description: 'Opening AutoShop settings...',
    });
  };

  // Listen for AI voice events from WhisperVoiceControl
  useEffect(() => {
    const handleAISearch = (event: CustomEvent) => {
      const { query } = event.detail;
      if (query) {
        console.log('🔍 AI Search command received:', query);
        // Clean the query before processing
        const cleanQuery = query.replace(/[.!?]+$/g, '').trim();
        console.log('🔍 Cleaned AI search query:', cleanQuery);
        handleVoiceSearchCommand(cleanQuery);
      }
    };

    const handleAIAutoshop = () => {
      console.log('🛒 AI AutoShop command received');
      handleVoiceAutoShopCommand();
    };

    // Listen for voice status updates
    const handleVoiceStatus = (event: CustomEvent) => {
      const { status, message } = event.detail;
      console.log('🎤 Voice status:', { status, message });

      // Show voice status in search bar
      if (status === 'listening') {
        setVoiceStatus('🎤 Listening... Speak now');
        setCurrentQuery('');
        console.log('🌈 Setting listening status for border animation');
      } else if (status === 'processing') {
        setVoiceStatus('🤖 Processing your request...');
      } else if (status === 'speaking') {
        setVoiceStatus('🗣️ DasWos AI is responding...');
      } else if (status === 'idle') {
        setVoiceStatus('');
        console.log('🌈 Clearing voice status - border animation should stop');
      } else if (message) {
        setVoiceStatus(message);
      }
    };

    // Listen for voice command results from WhisperVoiceControl
    const handleVoiceCommandResult = (event: CustomEvent) => {
      const { userQuery, aiResponse, audio } = event.detail;
      console.log('🎤 Voice command result:', { userQuery, aiResponse });

      if (userQuery) {
        // Clean the user query by removing trailing punctuation
        const cleanUserQuery = userQuery.replace(/[.!?]+$/g, '').trim();
        console.log('🔍 Cleaned user query:', cleanUserQuery);

        // Show what the user said in the search bar first and clear voice status
        setCurrentQuery(cleanUserQuery);
        setVoiceStatus('');

        // Force clear voice status to ensure input becomes editable
        setTimeout(() => {
          setVoiceStatus('');
        }, 50);

        // Show AI response in search bar
        if (aiResponse) {
          // Store AI response for display - ensure it's always a string
          const responseText = typeof aiResponse === 'string' ? aiResponse : (aiResponse.response || 'Processing...');

          // Show AI typing effect
          setVoiceStatus('🤖 DasWos AI: ' + responseText);

          // Handle different AI intents
          if (aiResponse.intent === 'search' && aiResponse.parameters?.query) {
            console.log('🔍 Executing search for:', aiResponse.parameters.query);
            // Update search query to what AI wants to search for
            setTimeout(() => {
              setCurrentQuery(aiResponse.parameters.query);
              setLastSubmittedQuery(aiResponse.parameters.query);
              setVoiceStatus('');
              onSearch(aiResponse.parameters.query);
            }, 2000);
          } else if (aiResponse.intent === 'navigation' && aiResponse.parameters?.route) {
            console.log('🧭 Navigating to:', aiResponse.parameters.route);
            setTimeout(() => {
              setVoiceStatus('');
              navigate(aiResponse.parameters.route);
            }, 2000);
          } else if (aiResponse.intent === 'autoshop') {
            console.log('🛒 Triggering AutoShop');
            setTimeout(() => {
              setVoiceStatus('');
              // Dispatch AutoShop event
              const autoshopEvent = new CustomEvent('aiAutoshop');
              window.dispatchEvent(autoshopEvent);
            }, 2000);
          } else {
            // For conversation responses, show the response in the search bar
            console.log('💬 Conversation response:', responseText);
            // Clear after 5 seconds
            setTimeout(() => {
              setVoiceStatus('');
            }, 5000);
          }
        }
      }
    };

    // Listen for direct voice search events
    const handleVoiceSearch = (event: CustomEvent) => {
      const { query } = event.detail;
      if (query) {
        console.log('🎤 Direct voice search received:', query);
        // Clear voice status immediately when voice search is received
        setVoiceStatus('');
        handleVoiceSearchCommand(query);
      }
    };

    window.addEventListener('aiSearch', handleAISearch as EventListener);
    window.addEventListener('aiAutoshop', handleAIAutoshop as EventListener);
    window.addEventListener('voiceCommandResult', handleVoiceCommandResult as EventListener);
    window.addEventListener('voiceStatus', handleVoiceStatus as EventListener);
    window.addEventListener('voiceSearch', handleVoiceSearch as EventListener);

    // Listen for AI Information Box state changes
    const handleAIStateChange = (event: CustomEvent) => {
      const { isLoading, hasContent } = event.detail;
      setAiIsLoading(isLoading);
      setAiHasContent(hasContent);
    };

    // Listen for AI Information Box cache events
    const handleAICacheResponse = (event: CustomEvent) => {
      const { context, response, conversationHistory } = event.detail;
      if (context && context.length > 0) {
        const contextKey = context.join(' -> ');
        console.log('💾 Caching AI response for context:', contextKey);

        // Cache the response with the conversation history
        setCachedResponses(prev => new Map(prev.set(contextKey, {
          response,
          conversationHistory,
          timestamp: Date.now()
        })));
      }
    };

    window.addEventListener('aiInformationStateChange', handleAIStateChange as EventListener);
    window.addEventListener('aiInformationCacheResponse', handleAICacheResponse as EventListener);

    return () => {
      window.removeEventListener('aiSearch', handleAISearch as EventListener);
      window.removeEventListener('aiAutoshop', handleAIAutoshop as EventListener);
      window.removeEventListener('voiceCommandResult', handleVoiceCommandResult as EventListener);
      window.removeEventListener('voiceStatus', handleVoiceStatus as EventListener);
      window.removeEventListener('voiceSearch', handleVoiceSearch as EventListener);
      window.removeEventListener('aiInformationStateChange', handleAIStateChange as EventListener);
      window.removeEventListener('aiInformationCacheResponse', handleAICacheResponse as EventListener);

      // Clean up AI response timeout
      if (aiResponseTimeoutId) {
        clearTimeout(aiResponseTimeoutId);
      }
    };
  }, [navigate, onSearch, toast]);

  // Function to reset search and go to home page
  const resetSearchAndGoHome = () => {
    // Clear localStorage first
    localStorage.removeItem('searchHistory');
    localStorage.removeItem('searchHistoryContexts');
    
    // Then clear all state
    setSearchHistory([]);
    setSearchHistoryContexts([]);
    setSearchContext([]);
    setCurrentQuery('');
    setSearchQuery('');
    setLastSubmittedQuery('');
    setIsQueryBeingEdited(false);
    setCurrentContextIndex(-1);
    
    // Clear the search input field
    if (searchInputRef.current) {
      searchInputRef.current.value = '';
    }
    
    // Notify parent component
    onNewSearch?.();
  };
  
  // Function to handle new search click
  const handleNewSearchClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    // Clear localStorage first
    localStorage.removeItem('searchHistory');
    localStorage.removeItem('searchHistoryContexts');
    
    // Then clear all state
    setSearchHistory([]);
    setSearchHistoryContexts([]);
    setSearchContext([]);
    setCurrentQuery('');
    setSearchQuery('');
    setLastSubmittedQuery('');
    setIsQueryBeingEdited(false);
    setCurrentContextIndex(-1);
    
    // Clear the search input field
    if (searchInputRef.current) {
      searchInputRef.current.value = '';
      searchInputRef.current.focus();
    }
    
    // Notify parent component
    onNewSearch?.();
  };

  // AI Information Box button handlers

  const handleAIRefresh = () => {
    // Trigger refresh functionality in AI Information Box
    const event = new CustomEvent('aiInformationRefresh');
    window.dispatchEvent(event);
  };

  const handleAIClear = () => {
    // Clear the search input
    setCurrentQuery('');
    setLastSubmittedQuery('');

    // Clear search context and history for fresh search (no refinement)
    setSearchContext([]);
    setSearchHistory([]);
    setSearchHistoryContexts([]);
    setCachedResponses(new Map());
    setCurrentContextIndex(-1);
    setIsQueryBeingEdited(false);
    setIsInputFocused(false);

    // Clear the search input field directly
    if (searchInputRef.current) {
      searchInputRef.current.value = '';
      searchInputRef.current.blur();
    }

    // Dispatch event to reset selectedResultType in parent component
    const resetEvent = new CustomEvent('clearSearchInterface', {
      detail: { clear: true }
    });
    window.dispatchEvent(resetEvent);

    // Trigger clear event for AI Information Box
    const event = new CustomEvent('aiInformationClear');
    window.dispatchEvent(event);
  };

  // No drag functionality - interface is fixed in position

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  const handleAiResponsePause = () => {
    setAiResponsePaused(true);
    // Clear the timeout so response doesn't disappear
    if (aiResponseTimeoutId) {
      clearTimeout(aiResponseTimeoutId);
      setAiResponseTimeoutId(null);
    }
  };

  const handleAiResponseClose = () => {
    setAiResponseText('');
    setAiResponsePaused(false);
    if (aiResponseTimeoutId) {
      clearTimeout(aiResponseTimeoutId);
      setAiResponseTimeoutId(null);
    }
  };

  const handleRemoveSearchTerm = (termToRemove: string, index: number, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent triggering the parent click event
    
    // Check if this is the last search term
    const isLastTerm = searchHistory.length === 1;
    
    // Remove from search history
    setSearchHistory(prev => {
      const newHistory = prev.filter((_, i) => i !== index);
      localStorage.setItem('searchHistory', JSON.stringify(newHistory));
      return newHistory;
    });

    // Remove the corresponding context from search history contexts
    setSearchHistoryContexts(prev => {
      const newContexts = prev.filter((_, i) => i !== index);
      localStorage.setItem('searchHistoryContexts', JSON.stringify(newContexts));
      return newContexts;
    });
    
    // Clear the current search context if it contains the removed term
    setSearchContext(prevContext => {
      const newContext = prevContext.filter(term => term !== termToRemove);
      return newContext;
    });
    
    // If we're removing the current context, reset to the default state
    if (currentContextIndex === index) {
      setCurrentContextIndex(-1);
      setCurrentQuery('');
      setLastSubmittedQuery('');
      
      // Clear the search input field
      if (searchInputRef.current) {
        searchInputRef.current.value = '';
      }
    }

    // If this was the last term, clear everything like the clear button does
    if (isLastTerm) {
      setCurrentQuery('');
      setSearchContext([]);
      setCurrentContextIndex(-1);
      setLastSubmittedQuery('');
      setIsQueryBeingEdited(false);
      
      // Clear the search input field
      if (searchInputRef.current) {
        searchInputRef.current.value = '';
        searchInputRef.current.blur();
      }
      
      // Clear all cached responses
      setCachedResponses(new Map());

      // Only clear results if we're currently showing them
      if (showResults) {
        onSearch?.('');
        // Also trigger a home navigation to ensure clean state
        onNewSearch?.();
      }

      // Force update the search input to be empty after a short delay
      setTimeout(() => {
        if (searchInputRef.current) {
          searchInputRef.current.value = '';
        }
        setCurrentQuery('');
        setLastSubmittedQuery('');
      }, 0);
    } else if (currentContextIndex > index) {
      // Adjust the current context index since we removed an item before it
      setCurrentContextIndex(prev => prev - 1);
    }

    // Remove cached response for the removed context
    const removedContext = searchHistoryContexts[index];
    if (removedContext) {
      const contextKey = removedContext.join(' -> ');
      setCachedResponses(prev => {
        const newMap = new Map(prev);
        newMap.delete(contextKey);
        return newMap;
      });
    }
  };

  const handleHistorySearch = (query: string, index: number) => {
    // Keep search bar empty for refinement
    setCurrentQuery('');

    // Find the corresponding context for this search history button
    // Each button represents a cumulative search context up to that point
    const targetContext = searchHistoryContexts[index] || [query];

    console.log('🔍 History search clicked:', {
      query,
      index,
      targetContext,
      currentContext: searchContext,
      availableContexts: searchHistoryContexts
    });

    // Set the search context to match the selected history level
    setSearchContext(targetContext);
    setCurrentContextIndex(index);

    // Check if we have a cached response for this context
    const contextKey = targetContext.join(' -> ');
    const cachedResponse = cachedResponses.get(contextKey);

    if (cachedResponse) {
      // If we have a cached response, dispatch it to the AI Information Box
      console.log('✅ Using cached response for context:', contextKey);
      const cachedEvent = new CustomEvent('aiInformationCachedResponse', {
        detail: {
          response: cachedResponse,
          context: targetContext,
          query: query
        }
      });
      window.dispatchEvent(cachedEvent);
    } else {
      // If no cached response, trigger a new search
      console.log('🔄 No cached response, triggering new search for:', contextKey);
      onSearch(query);
    }

    setLastSubmittedQuery(query);
    setIsQueryBeingEdited(false);

    // Don't change the order of search history when clicking on history buttons
    // The order should remain as the chronological order of searches
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // If AI mode is enabled, has shopping results, not being edited, not focused, and we have a buy handler, trigger buy
    if (aiModeEnabled && hasShoppingResults && selectedResultType === 'shopping' && !isQueryBeingEdited && !isInputFocused && onBuyCurrentProduct) {
      onBuyCurrentProduct();
      return;
    }

    // Otherwise, perform smart AI processing or normal search
    if (currentQuery.trim()) {
      const queryToSearch = currentQuery.trim();

      // Process query through smart AI assistant
      const aiResult = processSmartAIQuery(queryToSearch);

      if (aiResult.canAnswerBriefly && aiResult.response) {
        // Show brief response in the search bar placeholder
        setAiResponseText(aiResult.response);
        setCurrentQuery(''); // Clear the input
        setAiResponsePaused(false); // Reset pause state

        // Clear any existing timeout
        if (aiResponseTimeoutId) {
          clearTimeout(aiResponseTimeoutId);
        }

        // Clear the AI response after 8 seconds (unless paused)
        const timeoutId = setTimeout(() => {
          if (!aiResponsePaused) {
            setAiResponseText('');
            setAiResponseTimeoutId(null);
          }
        }, 8000);
        setAiResponseTimeoutId(timeoutId);

        return;
      } else if (aiResult.shouldNavigateTo) {
        // Navigate to appropriate page and perform search there
        if (aiResult.shouldNavigateTo === 'shopping') {
          // Set up for shopping search
          onSearchTypeChange && onSearchTypeChange('shopping');
        } else if (aiResult.shouldNavigateTo === 'information') {
          // Set up for information search
          onSearchTypeChange && onSearchTypeChange('information');
        }
      }

      // Handle search context based on refine mode
      if (refineMode) {
        // In refine mode, build search context
        let newSearchContext = [...searchContext];
        
        // Only add the query if it's not the same as the last search
        if (newSearchContext[newSearchContext.length - 1] !== queryToSearch) {
          newSearchContext.push(queryToSearch);
        } else {
          console.log('🔁 Ignoring duplicate search term in refine mode');
        }
        
        // If we have no context yet (first search after toggling refine mode on),
        // load the full search history as context
        if (newSearchContext.length === 0 && searchHistory.length > 0) {
          console.log('🔄 Loading search history as initial context');
          newSearchContext = [...searchHistory];
        }
        
        setSearchContext(newSearchContext);

        // Add to search history (avoid duplicates and limit to 5 recent searches)
        setSearchHistory(prev => {
          const filtered = prev.filter(q => q !== queryToSearch);
          const newHistory = [queryToSearch, ...filtered].slice(0, 5);
          // Save to localStorage
          localStorage.setItem('searchHistory', JSON.stringify(newHistory));
          return newHistory;
        });

        // Update search history contexts - each entry represents cumulative context up to that point
        setSearchHistoryContexts(prev => {
          const newContexts = [...prev];
          // Add the new cumulative context if it's not empty
          if (newSearchContext.length > 0) {
            newContexts.unshift([...newSearchContext]);
            const finalContexts = newContexts.slice(0, 5);
            console.log('📝 Updated search history contexts:', finalContexts);
            // Save to localStorage
            localStorage.setItem('searchHistoryContexts', JSON.stringify(finalContexts));
            return finalContexts;
          }
          return newContexts;
        });

        // Update current context index
        setCurrentContextIndex(0);
      } else {
        // When refine mode is off, clear any existing context
        setSearchContext([queryToSearch]);
        setSearchHistoryContexts([[queryToSearch]]);
        setCurrentContextIndex(-1);
        
        // Still update search history but without context
        setSearchHistory(prev => {
          const filtered = prev.filter(q => q !== queryToSearch);
          const newHistory = [queryToSearch, ...filtered].slice(0, 5);
          localStorage.setItem('searchHistory', JSON.stringify(newHistory));
          return newHistory;
        });
      }
      
      console.log('🎯 Search submitted with refine mode:', refineMode);

      // Clear the input immediately for all searches to make refinement easier
      setCurrentQuery('');

      onSearch(queryToSearch);
      setLastSubmittedQuery(queryToSearch);
      setIsQueryBeingEdited(false);
      // Blur the input to exit focus mode
      if (searchInputRef.current) {
        searchInputRef.current.blur();
      }
      // For shopping mode, keep the query for display in results
    }
  };



  return (
    <div
      ref={containerRef}
      className={`search-interface ${className}`}
      style={{
        position: 'relative',
        width: '932px', // Fixed width for the search bar
        margin: '0 auto',
        backgroundColor: 'transparent',
        padding: showResults ? '10px 0 0 0' : '15px 0 0 0',
        transition: 'all 0.3s ease',
        overflow: 'hidden',
        marginTop: showResults ? '-50px' : '0', // Move up when showing results
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: showResults ? 'auto' : '30vh', // Center vertically when no results
        transform: 'translateX(-40px)' // Shift left to balance the spacing
      }}
    >
      {/* No drag handle needed */}

      {/* Logo and buttons - only shown when no results */}
      {!showResults && (
        <div className="flex flex-col items-center justify-center mb-3">
          <div className="relative inline-block">
            <div className="py-1 flex justify-center">
              <div
                onClick={resetSearchAndGoHome}
                className="cursor-pointer hover:opacity-80 transition-opacity"
                title="Return to home page"
              >
                <DasWosLogo height={40} width="auto" />
              </div>
            </div>

            {/* Animated Trust Heading */}
            <div className="mt-1 mb-2 w-full text-center text-xs">
              <AnimatedTrustText
                sentences={[
                  "Helping you find what you need with confidence."
                ]}
                duration={5000}
              />
            </div>

            {/* Buttons container */}
            <div className="absolute right-[-60px] top-1/2 transform -translate-y-1/2 flex items-center space-x-3">
              {/* Theme Toggle Button */}
              <button
                onClick={toggleTheme}
                className="bg-transparent flex items-center justify-center w-8 h-8 text-xs rounded-full hover:bg-gray-100/30 dark:hover:bg-gray-800/30"
                aria-label={theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
                title={theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
              >
                {theme === 'dark' ? (
                  <Sun className="h-5 w-5 text-gray-400" />
                ) : (
                  <Moon className="h-5 w-5 text-gray-400" />
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Search form */}
      <form onSubmit={handleSubmit} className={`flex flex-col space-y-2 px-4 pb-2 ${showResults ? 'mt-0' : ''} w-full`}>
        {showResults && (
          <div className="flex items-center mb-1.5">
            <div
              onClick={resetSearchAndGoHome}
              className="cursor-pointer hover:opacity-80 transition-opacity"
              title="Return to home page"
            >
              <DasWosLogo height={30} width="auto" className="mr-3" />
            </div>
            <div className="flex-1"></div>

            {/* Theme Toggle Button when results are shown */}
            <button
              onClick={toggleTheme}
              className="bg-transparent flex items-center justify-center w-8 h-8 text-xs rounded-full hover:bg-gray-100/30 dark:hover:bg-gray-800/30"
              aria-label={theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
              title={theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
            >
              {theme === 'dark' ? (
                <Sun className="h-5 w-5 text-gray-400" />
              ) : (
                <Moon className="h-5 w-5 text-gray-400" />
              )}
            </button>
          </div>
        )}
        <div className="relative flex items-center gap-2 w-full max-w-[932px] mx-auto">
          {/* Voice control removed - using bottom right corner voice button instead */}

          {/* Main search container */}
          <div className="relative flex w-full">
            {/* Search bar container - responsive to theme */}
            <div className="relative flex w-full bg-gray-100 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm overflow-hidden z-10">



            <input
              type="text"
              placeholder={
                voiceStatus
                  ? voiceStatus
                  : aiModeEnabled && aiResponseText
                  ? `DasWos AI: ${aiResponseText}`
                  : showResults && (selectedResultType === 'information' || selectedResultType === 'shopping')
                  ? "Refine your search here"
                  : "How can I help?"
              }
              value={voiceStatus ? '' : currentQuery}
              onChange={(e) => handleQueryChange(e.target.value)}
              onFocus={handleInputFocus}
              onBlur={handleInputBlur}
              maxLength={100}
              className={`w-full px-4 py-2 text-sm bg-transparent focus:outline-none border-0 ${aiResponseText ? 'rounded-none' : 'rounded-l-md'} h-[38px] ${
                voiceStatus
                  ? 'text-blue-400 placeholder-blue-400'
                  : 'text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400'
              }`}
              ref={searchInputRef}
              readOnly={!!voiceStatus}
            />

            {/* AI Response Pause/Close Button */}
            {aiModeEnabled && aiResponseText && (
              <button
                type="button"
                onClick={aiResponsePaused ? handleAiResponseClose : handleAiResponsePause}
                className="px-2 bg-blue-600 hover:bg-blue-700 text-white border-l border-gray-600 transition-colors duration-200 h-[38px] flex items-center justify-center"
                title={aiResponsePaused ? "Close AI response" : "Pause AI response"}
              >
                {aiResponsePaused ? (
                  <X className="h-4 w-4" />
                ) : (
                  <Pause className="h-4 w-4" />
                )}
              </button>
            )}

            <button
              type="submit"
              className={`px-4 search-button ${aiResponseText ? 'rounded-r-md' : 'rounded-r-md'} border-l ${
                aiModeEnabled && hasShoppingResults && selectedResultType === 'shopping' && !isQueryBeingEdited && !isInputFocused
                  ? 'bg-green-600 hover:bg-green-700 text-white border-gray-600'
                  : selectedResultType === 'information'
                  ? 'bg-blue-600 hover:bg-blue-700 text-white border-gray-600'
                  : selectedResultType === 'shopping'
                  ? 'bg-blue-600 hover:bg-blue-700 text-white border-gray-600'
                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 border-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-100 dark:border-gray-600'
              } transition-colors duration-200 h-[38px] flex items-center justify-center`}
              aria-label={
                aiModeEnabled && hasShoppingResults && selectedResultType === 'shopping' && !isQueryBeingEdited && !isInputFocused
                  ? "Execute Purchase"
                  : selectedResultType === 'information'
                  ? "Refine Information"
                  : selectedResultType === 'shopping'
                  ? "Refine Shopping"
                  : "Search"
              }
            >
              {/* Button content */}
              <div className="flex items-center gap-1">
                {aiModeEnabled && hasShoppingResults && selectedResultType === 'shopping' && !isQueryBeingEdited && !isInputFocused ? (
                  <>
                    <ShoppingCart className="h-4 w-4" />
                    <span className="text-xs font-semibold">BUY</span>
                  </>
                ) : (selectedResultType === 'information' || selectedResultType === 'shopping') ? (
                  <>
                    <RefreshCw className="h-4 w-4" />
                    <span className="text-xs font-semibold">REFINE</span>
                  </>
                ) : refineMode ? (
                  <Sparkles className={`h-4 w-4 ${aiModeEnabled ? 'text-blue-500' : 'text-gray-600 dark:text-gray-300'}`} />
                ) : (
                  <Search className={`h-4 w-4 ${aiModeEnabled ? 'text-blue-500' : 'text-gray-600 dark:text-gray-300'}`} />
                )}
              </div>
            </button>


            </div>
          </div>
        </div>

        {/* Search History - directly under search bar, left-aligned */}
        {searchHistory.length > 0 && (
          <div className="mt-2 w-full max-w-[932px] mx-auto">
            <div className="flex flex-wrap gap-2">
              {searchHistory.map((query, index) => {
                // Display only the individual search term, not the cumulative context
                const displayText = query;
                const isCurrentContext = currentContextIndex === index;

                return (
                  <div
                    key={`${query}-${index}`}
                    className={`relative group px-3 py-1.5 text-xs rounded-full border transition-all duration-200 shadow-sm ${
                      isCurrentContext
                        ? 'bg-blue-600 text-white border-blue-500 hover:bg-blue-700'
                        : 'bg-gray-800 text-gray-100 border-gray-600 hover:bg-gray-700 hover:border-gray-500'
                    }`}
                  >
                    <button
                      onClick={() => handleHistorySearch(query, index)}
                      className="pr-5"
                      title={`Search for: ${displayText}`}
                    >
                      {displayText}
                    </button>

                    {/* X button */}
                    <button
                      onClick={(e) => handleRemoveSearchTerm(query, index, e)}
                      className={`absolute top-0 right-0 w-4 h-4 rounded-full flex items-center justify-center transition-all duration-200 ${
                        isCurrentContext
                          ? 'bg-blue-700 hover:bg-blue-800 text-white'
                          : 'bg-gray-700 hover:bg-gray-600 text-gray-300 hover:text-white'
                      } opacity-0 group-hover:opacity-100 transform translate-x-1 -translate-y-1`}
                      title={`Remove "${displayText}" from search`}
                    >
                      <X className="h-2.5 w-2.5" />
                    </button>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Feature buttons - moved down to make room for search history */}
        <div className="flex justify-center mt-3 space-x-2 w-full max-w-[932px] mx-auto">
          {/* Show SafeCardIndicator when safe card is active, otherwise show toggles */}
          {isLocked ? (
            <SafeCardIndicator />
          ) : (
            <>
              {/* SafeSphere button - always dark styled */}
              <div
                className={`bg-gray-800 rounded-sm shadow-sm border border-gray-600 inline-flex items-center px-2 py-1 ${activeSphere === 'safesphere' ? 'w-[160px]' : 'w-[120px]'} cursor-pointer transition-all duration-200`}
                onClick={() => onSphereChange && onSphereChange(activeSphere === 'safesphere' ? 'opensphere' : 'safesphere')}
              >
            {/* Square checkbox - always dark styled */}
            <div className="w-4 h-4 border border-gray-500 bg-gray-700 flex items-center justify-center mr-2 flex-shrink-0">
              {activeSphere === 'safesphere' && (
                <svg className="w-4 h-4 text-gray-200" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M5 12l5 5L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                </svg>
              )}
            </div>

            {/* Shield icon - always dark styled */}
            <svg className="h-3.5 w-3.5 mr-1.5 text-gray-300 flex-shrink-0" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
            </svg>

            {/* Text - always dark styled */}
            <span className="text-gray-100 font-medium text-xs flex-shrink-0 whitespace-nowrap w-[70px]">SafeSphere</span>

            {/* Status label - only shown when active */}
            {activeSphere === 'safesphere' && (
              <span className="ml-auto text-green-500 text-[8px] font-medium w-[55px] text-right pr-1">Protected</span>
            )}
          </div>

          {/* DasWos AI is now always enabled - no toggle needed */}

          {/* SuperSafe button - always dark styled */}
          <div
            className={`bg-gray-800 rounded-sm shadow-sm border border-gray-600 inline-flex items-center px-2 py-1 ${superSafeActive ? 'w-[160px]' : 'w-[120px]'} cursor-pointer transition-all duration-200`}
            onClick={() => onToggleSuperSafe && onToggleSuperSafe(!superSafeActive)}
          >
            {/* Square checkbox - always dark styled */}
            <div className="w-4 h-4 border border-gray-500 bg-gray-700 flex items-center justify-center mr-2 flex-shrink-0">
              {superSafeActive && (
                <svg className="w-4 h-4 text-gray-200" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M5 12l5 5L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                </svg>
              )}
            </div>

            {/* Circle check icon - always dark styled */}
            <svg className="h-3.5 w-3.5 mr-1.5 text-gray-300 flex-shrink-0" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
            </svg>

            {/* Text - always dark styled */}
            <span className="text-gray-100 font-medium text-xs flex-shrink-0 whitespace-nowrap w-[70px]">SuperSafe</span>

            {/* Status label - only shown when active */}
            {superSafeActive && (
              <span className="ml-auto text-green-500 text-[8px] font-medium w-[35px] text-right pr-1">Active</span>
            )}
          </div>

          {/* Earn Points button - always dark styled */}
          <div
            className={`bg-gray-800 rounded-sm shadow-sm border border-gray-600 inline-flex items-center px-2 py-1 ${isEarnPointsEnabled ? 'w-[160px]' : 'w-[120px]'} cursor-pointer transition-all duration-200`}
            onClick={() => toggleEarnPoints(!isEarnPointsEnabled)}
          >
            {/* Square checkbox - always dark styled */}
            <div className="w-4 h-4 border border-gray-500 bg-gray-700 flex items-center justify-center mr-2 flex-shrink-0">
              {isEarnPointsEnabled && (
                <svg className="w-4 h-4 text-gray-200" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M5 12l5 5L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                </svg>
              )}
            </div>

            {/* Coins icon - always dark styled */}
            <svg className="h-3.5 w-3.5 mr-1.5 text-gray-300 flex-shrink-0" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="8" cy="8" r="6" stroke="currentColor" strokeWidth="2"/>
              <path d="M18.09 10.37A6 6 0 1 1 10.37 18.09" stroke="currentColor" strokeWidth="2"/>
              <path d="M7 6h1v4" stroke="currentColor" strokeWidth="2"/>
              <path d="M9.5 6H8.5a1 1 0 0 0 0 2h1a1 1 0 0 1 0 2H8" stroke="currentColor" strokeWidth="2"/>
            </svg>

            {/* Text - always dark styled */}
            <span className="text-gray-100 font-medium text-xs flex-shrink-0 whitespace-nowrap w-[70px]">Earn Points</span>

            {/* Status label - only shown when active */}
            {isEarnPointsEnabled && (
              <span className="ml-auto text-green-500 text-[8px] font-medium w-[35px] text-right pr-1">Active</span>
            )}
          </div>
            </>
          )}
        </div>

      </form>

      {/* Mode Control Buttons - above search results */}
      {aiModeEnabled && isAiConversationActive && !isAskingIfShopping && showResults && (
        <div className="flex justify-center mt-2 mb-1 w-full max-w-[932px] mx-auto">
          <div className="flex justify-between items-center p-2 bg-gray-800 rounded-lg border border-gray-600 w-full max-w-[932px]">
            <div className="flex items-center space-x-3">
              {/* Mode toggle */}
              <button
                type="button"
                onClick={() => onSearchTypeChange && onSearchTypeChange(searchType === 'shopping' ? 'information' : 'shopping')}
                className="text-blue-400 hover:text-blue-300 flex items-center text-xs"
                title={`Switch to ${searchType === 'shopping' ? 'information' : 'shopping'} mode`}
              >
                <span className={`inline-block w-2 h-2 rounded-full mr-1 ${searchType === 'shopping' ? 'bg-green-500' : 'bg-blue-500'}`}></span>
                {searchType === 'shopping' ? 'Shopping' : 'Information'} mode
              </button>

              {/* Refine mode toggle */}
              <button
                type="button"
                onClick={toggleRefineMode}
                className={`flex items-center text-xs ${refineMode ? 'text-blue-400' : 'text-gray-400'} hover:text-blue-300`}
                title={refineMode ? 'Turn off refine mode' : 'Turn on refine mode'}
              >
                <Sparkles className={`h-3 w-3 mr-1 ${refineMode ? 'text-yellow-400' : 'text-gray-400'}`} />
                Refine: {refineMode ? 'ON' : 'OFF'}
              </button>

              {/* New search button */}
              <button
                type="button"
                onClick={handleNewSearchClick}
                className="text-blue-400 hover:text-blue-300 flex items-center text-xs"
              >
                <X className="h-3 w-3 mr-1" />
                New search
              </button>

              {/* AI Information Box buttons - only show in information mode */}
              {searchType === 'information' && (
                <>


                  <button
                    type="button"
                    onClick={() => setIsAIBoxExpanded(!isAIBoxExpanded)}
                    className="text-blue-400 hover:text-blue-300 flex items-center text-xs"
                    title={isAIBoxExpanded ? "Minimize AI response" : "Expand AI response"}
                  >
                    {isAIBoxExpanded ? (
                      <>
                        <Minimize className="h-3 w-3 mr-1" />
                        Minimize
                      </>
                    ) : (
                      <>
                        <Expand className="h-3 w-3 mr-1" />
                        Expand
                      </>
                    )}
                  </button>

                  {/* Refresh button */}
                  <button
                    type="button"
                    onClick={handleAIRefresh}
                    className="text-blue-400 hover:text-blue-300 flex items-center text-xs"
                    title="Refresh AI information"
                    disabled={aiIsLoading}
                  >
                    <RefreshCw className={`h-3 w-3 mr-1 ${aiIsLoading ? 'animate-spin' : ''}`} />
                    Refresh
                  </button>

                  {/* Clear button */}
                  <button
                    type="button"
                    onClick={handleAIClear}
                    className="text-blue-400 hover:text-blue-300 flex items-center text-xs"
                    title="Clear search and results"
                  >
                    <Eraser className="h-3 w-3 mr-1" />
                    Clear
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Search Results */}
      {showResults && selectedResultType && (
        <div className="w-full max-w-[932px] mx-auto">
          {selectedResultType === 'shopping' ? (
            <ShoppingResults
              searchQuery={searchQuery}
              searchContext={searchContext}
              sphere={activeSphere}
              className="mt-2"
              aiModeEnabled={aiModeEnabled}
              onCurrentProductChange={onCurrentProductChange}
            />
          ) : (
            <InformationResults
              searchQuery={searchHistory.length === 0 ? '' : (searchContext.length > 0 ? searchContext.join(' ') : searchQuery)}
              searchContext={searchHistory.length === 0 ? [] : searchContext}
              sphere={activeSphere}
              className="mt-2"
              isAIBoxExpanded={isAIBoxExpanded}
              onAIBoxExpandChange={setIsAIBoxExpanded}
              refineMode={refineMode}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default SearchInterface;

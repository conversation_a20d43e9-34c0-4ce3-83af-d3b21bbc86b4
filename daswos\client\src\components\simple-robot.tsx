import React, { useState, useEffect, useCallback } from 'react';

interface Position {
  x: number;
  y: number;
}

interface DaswosRobotProps {
  className?: string;
}

const DaswosRobot: React.FC<DaswosRobotProps> = ({ className = '' }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState<Position>({ x: 0, y: 0 });
  const [robotState, setRobotState] = useState<'idle' | 'dance' | 'walk'>('idle');

  // Game state
  const [isGameActive, setIsGameActive] = useState(false);
  const [userGuess, setUserGuess] = useState<Position | null>(null);
  const [actualPosition, setActualPosition] = useState<Position | null>(null);
  const [showResult, setShowResult] = useState(false);
  const [lightsOn, setLightsOn] = useState(true);

  // Generate random position within screen bounds
  const generateRandomPosition = useCallback((): Position => {
    const margin = 100;
    const x = Math.random() * (window.innerWidth - 2 * margin) + margin;
    const y = Math.random() * (window.innerHeight - 2 * margin) + margin;
    return { x, y };
  }, []);

  // Show robot randomly
  useEffect(() => {
    if (isGameActive) return; // Don't show random robot during game

    const showRobot = () => {
      // Random chance to show robot (50% chance every 10 seconds for testing)
      if (Math.random() < 0.5) {
        const newPosition = generateRandomPosition();
        setPosition(newPosition);
        setIsVisible(true);

        // Random robot state (30% chance to spin)
        const states: ('idle' | 'dance' | 'walk')[] = ['idle', 'idle', 'dance', 'walk'];
        setRobotState(states[Math.floor(Math.random() * states.length)]);

        // Hide after 8-15 seconds
        const hideDelay = 8000 + Math.random() * 7000;
        setTimeout(() => {
          if (!isGameActive) {
            setIsVisible(false);
          }
        }, hideDelay);
      }
    };

    // Check every 10 seconds for testing
    const interval = setInterval(showRobot, 10000);

    // Also show immediately for testing
    setTimeout(showRobot, 1000);

    return () => clearInterval(interval);
  }, [isGameActive, generateRandomPosition]);

  // Handle robot click to start game
  const handleRobotClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    console.log('Robot clicked! Starting game...');

    if (isGameActive) return;

    setIsGameActive(true);
    setIsVisible(false);
    setLightsOn(false);
    setUserGuess(null);
    setShowResult(false);

    // Generate actual robot position for the game
    const newActualPosition = generateRandomPosition();
    setActualPosition(newActualPosition);

    console.log('Game started, actual position:', newActualPosition);
  };

  // Handle screen click for guess placement
  const handleScreenClick = (event: React.MouseEvent) => {
    if (!isGameActive || lightsOn || userGuess) return;

    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    setUserGuess({ x, y });
  };

  // Handle light switch toggle
  const handleLightSwitch = () => {
    if (!userGuess || !actualPosition) return;

    setLightsOn(true);
    setShowResult(true);
  };

  // Handle exit game
  const handleExitGame = () => {
    setIsGameActive(false);
    setLightsOn(true);
    setUserGuess(null);
    setActualPosition(null);
    setShowResult(false);
  };

  // Handle play again
  const handlePlayAgain = () => {
    setLightsOn(false);
    setUserGuess(null);
    setShowResult(false);

    // Generate new actual position
    const newActualPosition = generateRandomPosition();
    setActualPosition(newActualPosition);
  };

  // Calculate distance between guess and actual position
  const calculateDistance = (): number => {
    if (!userGuess || !actualPosition) return 0;
    const dx = userGuess.x - actualPosition.x;
    const dy = userGuess.y - actualPosition.y;
    return Math.sqrt(dx * dx + dy * dy);
  };

  // Determine if guess was close enough (within 100 pixels)
  const isGuessCorrect = (): boolean => {
    return calculateDistance() <= 100;
  };

  // Calculate optimal position for result dialog to avoid covering markers
  const getDialogPosition = () => {
    if (!userGuess || !actualPosition) return { top: '50%', left: '50%' };

    const centerY = (userGuess.y + actualPosition.y) / 2;
    const centerX = (userGuess.x + actualPosition.x) / 2;

    // If markers are in the center area, move dialog to top or bottom
    if (centerY > window.innerHeight * 0.3 && centerY < window.innerHeight * 0.7) {
      // Move to top if markers are in center/bottom, bottom if in top
      const dialogY = centerY > window.innerHeight * 0.5 ? '20%' : '80%';
      return { top: dialogY, left: '50%' };
    }

    // If markers are in left/right areas, move dialog to opposite side
    if (centerX < window.innerWidth * 0.3) {
      return { top: '50%', left: '70%' };
    } else if (centerX > window.innerWidth * 0.7) {
      return { top: '50%', left: '30%' };
    }

    // Default center position
    return { top: '50%', left: '50%' };
  };

  // Game overlay render
  if (isGameActive) {
    return (
      <div
        className="fixed inset-0 z-50 cursor-crosshair"
        style={{
          backgroundColor: lightsOn ? 'rgba(0, 0, 0, 0.1)' : 'rgba(0, 0, 0, 0.95)',
          transition: 'background-color 0.5s ease'
        }}
        onClick={handleScreenClick}
      >
        {/* Exit button */}
        <button
          onClick={handleExitGame}
          className="absolute top-4 right-4 w-12 h-12 bg-white hover:bg-gray-100 text-black border border-black rounded-lg flex items-center justify-center text-xl font-bold z-60 transition-colors dark:bg-gray-800 dark:text-white dark:border-gray-400 dark:hover:bg-gray-700"
        >
          ×
        </button>

        {/* Instructions */}
        {!lightsOn && !userGuess && (
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white text-center">
            <p className="text-lg">Click anywhere to guess where the robot will appear</p>
          </div>
        )}

        {/* User's guess marker */}
        {userGuess && (
          <div
            className="absolute w-8 h-8 bg-blue-500 rounded-lg border-2 border-black transform -translate-x-1/2 -translate-y-1/2 z-50"
            style={{ left: userGuess.x, top: userGuess.y }}
          >
            <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-white text-black px-2 py-1 rounded border border-black text-xs font-medium whitespace-nowrap">
              Your Guess
            </div>
          </div>
        )}

        {/* Light switch button */}
        {userGuess && !lightsOn && (
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
            <button
              onClick={handleLightSwitch}
              className="bg-black hover:bg-gray-700 text-white px-8 py-4 rounded-lg font-medium text-lg transition-colors flex items-center gap-2 border border-black"
            >
              💡 Turn On Lights
            </button>
          </div>
        )}

        {/* Actual robot position (only visible when lights are on) */}
        {lightsOn && actualPosition && (
          <div
            className="absolute transform -translate-x-1/2 -translate-y-1/2 z-50"
            style={{ left: actualPosition.x, top: actualPosition.y }}
          >
            {/* Robot image */}
            <img
              src="/daswos_redesign_correct_logo_simple.png"
              alt="DasWos Robot"
              className="w-20 h-20 object-contain"
              onError={(e) => {
                console.log('Game image failed to load from:', e.currentTarget.src);
              }}
            />
          </div>
        )}

        {/* Result display */}
        {showResult && userGuess && actualPosition && (
          <div
            className="fixed transform -translate-x-1/2 -translate-y-1/2 bg-gray-700/90 backdrop-blur-sm rounded p-6 text-center w-80 h-48 z-[9999] text-white flex flex-col justify-center"
            style={getDialogPosition()}
          >
            <h3 className="text-2xl font-medium mb-4 text-white">
              {isGuessCorrect() ? 'Close, but not quite!' : 'Close, but not quite!'}
            </h3>
            <p className="text-lg mb-2 text-white">
              Distance: <span className="font-medium">{Math.round(calculateDistance())} pixels</span>
            </p>
            <p className="text-sm text-gray-300 mb-6">
              Try to get within 100 pixels next time!
            </p>
            <div className="flex gap-3 justify-center">
              <button
                onClick={handlePlayAgain}
                className="bg-black hover:bg-gray-800 text-white px-6 py-2 rounded font-medium transition-colors"
              >
                Play Again
              </button>
              <button
                onClick={handleExitGame}
                className="bg-gray-600 hover:bg-gray-500 text-white px-6 py-2 rounded font-medium transition-colors"
              >
                Exit Game
              </button>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Regular robot display
  if (!isVisible) return null;

  return (
    <div
      className={`fixed z-50 cursor-pointer transition-all duration-500 hover:scale-110 pointer-events-auto ${className}`}
      style={{
        left: position.x,
        top: position.y,
        transform: 'translate(-50%, -50%)',
        pointerEvents: 'auto'
      }}
      onClick={handleRobotClick}
      onMouseDown={(e) => e.stopPropagation()}
    >
      {/* Robot image */}
      <img
        src="/daswos_redesign_correct_logo_simple.png"
        alt="DasWos Robot"
        className={`w-20 h-20 object-contain ${
          robotState === 'dance' ? 'animate-spin' :
          robotState === 'walk' ? 'animate-pulse' :
          'animate-none'
        }`}
        onError={(e) => {
          console.log('Image failed to load from:', e.currentTarget.src);
        }}
        onLoad={() => {
          console.log('Robot image loaded successfully!');
        }}
      />
    </div>
  );
};

export default DaswosRobot;

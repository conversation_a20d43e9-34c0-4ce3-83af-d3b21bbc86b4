import React, { useState, useEffect } from 'react';

interface SimpleRobotProps {
  className?: string;
}

const SimpleRobot: React.FC<SimpleRobotProps> = ({ className = '' }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [robotState, setRobotState] = useState<'idle' | 'dance' | 'walk'>('idle');

  // Show robot randomly
  useEffect(() => {
    const showRobot = () => {
      // Random chance to show robot (10% chance every 30 seconds)
      if (Math.random() < 0.1) {
        // Random position on screen
        const x = Math.random() * (window.innerWidth - 200) + 100;
        const y = Math.random() * (window.innerHeight - 200) + 100;
        
        setPosition({ x, y });
        setIsVisible(true);
        
        // Random robot state
        const states: ('idle' | 'dance' | 'walk')[] = ['idle', 'dance', 'walk'];
        setRobotState(states[Math.floor(Math.random() * states.length)]);
        
        // Hide after 5-10 seconds
        const hideDelay = 5000 + Math.random() * 5000;
        setTimeout(() => {
          setIsVisible(false);
        }, hideDelay);
      }
    };

    // Check every 30 seconds
    const interval = setInterval(showRobot, 30000);
    
    // Also show immediately with lower chance
    setTimeout(showRobot, 2000);

    return () => clearInterval(interval);
  }, []);

  if (!isVisible) return null;

  return (
    <div 
      className={`fixed z-50 pointer-events-none transition-all duration-500 ${className}`}
      style={{ 
        left: position.x, 
        top: position.y,
        transform: 'translate(-50%, -50%)'
      }}
    >
      <div className={`w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold shadow-lg ${
        robotState === 'dance' ? 'animate-bounce' : 
        robotState === 'walk' ? 'animate-pulse' : 
        'animate-none'
      }`}>
        🤖
      </div>
      
      {/* Simple speech bubble */}
      <div className="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-white rounded-lg px-3 py-1 text-xs shadow-lg border">
        {robotState === 'dance' ? '💃 Dancing!' : 
         robotState === 'walk' ? '🚶 Walking!' : 
         '👋 Hello!'}
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-white"></div>
      </div>
    </div>
  );
};

export default SimpleRobot;

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { useLocation } from 'wouter';
import { useToast } from '@/hooks/use-toast';
import { useEarnPoints } from '@/contexts/earn-points-context';
import SignupPromptDialog from './signup-prompt-dialog';

interface Position {
  x: number;
  y: number;
}

interface DaswosRobotProps {
  className?: string;
}

const DaswosRobot: React.FC<DaswosRobotProps> = ({ className = '' }) => {
  const { user } = useAuth();
  const [location, setLocation] = useLocation();
  const { toast } = useToast();
  const { isEarnPointsEnabled } = useEarnPoints();

  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState<Position>({ x: 0, y: 0 });
  const [robotState, setRobotState] = useState<'idle' | 'dance' | 'walk'>('idle');

  // Game state
  const [isGameActive, setIsGameActive] = useState(false);
  const [userGuess, setUserGuess] = useState<Position | null>(null);
  const [actualPosition, setActualPosition] = useState<Position | null>(null);
  const [showResult, setShowResult] = useState(false);
  const [lightsOn, setLightsOn] = useState(true);
  const [showSignupPrompt, setShowSignupPrompt] = useState(false);
  const [cursorPosition, setCursorPosition] = useState<Position>({ x: 0, y: 0 });

  // Game options state
  const [showGameOptions, setShowGameOptions] = useState(false);
  const [autoRestart, setAutoRestart] = useState(true);
  const [autoLights, setAutoLights] = useState(true);

  // Session tracking
  const [sessionCoins, setSessionCoins] = useState(0);
  const [showCongratulations, setShowCongratulations] = useState(false);
  const [totalGuesses, setTotalGuesses] = useState(0);

  // Game limits
  const MAX_GUESSES = 10;

  // Generate random position within screen bounds
  const generateRandomPosition = useCallback((): Position => {
    const margin = 100;
    const x = Math.random() * (window.innerWidth - 2 * margin) + margin;
    const y = Math.random() * (window.innerHeight - 2 * margin) + margin;
    return { x, y };
  }, []);

  // Store pending reward in localStorage
  const storePendingReward = () => {
    const pendingRewards = JSON.parse(localStorage.getItem('pendingDasWosRewards') || '[]');
    const newReward = {
      id: Date.now() + Math.random(), // Unique ID
      type: 'robot_game',
      amount: 1,
      description: 'Robot Game - Correct Guess',
      timestamp: new Date().toISOString()
    };
    pendingRewards.push(newReward);
    localStorage.setItem('pendingDasWosRewards', JSON.stringify(pendingRewards));
    console.log('Stored pending reward:', newReward);
  };

  // Award DasWos coin for correct guess
  const awardCoin = async () => {
    // Update session counter
    setSessionCoins(prev => prev + 1);

    if (user) {
      try {
        const response = await fetch('/api/user/daswos-coins/game-reward', {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        const data = await response.json();

        if (data.success) {
          // Show congratulations dialog instead of toast
          setShowCongratulations(true);
        }
      } catch (error) {
        console.error('Error awarding coin:', error);
        // Still show congratulations even if API fails
        setShowCongratulations(true);
      }
    } else {
      // Store pending reward for when user logs in
      storePendingReward();
      // Show congratulations dialog
      setShowCongratulations(true);
    }
  };



  // Check if user is on auth page
  const isOnAuthPage = location === '/auth';

  // Show robot randomly
  useEffect(() => {
    // Don't show random robot if earn points is disabled, during game, or on auth page
    if (!isEarnPointsEnabled || isGameActive || isOnAuthPage) return;

    const showRobot = () => {
      // Random chance to show robot (50% chance every 10 seconds for testing)
      if (Math.random() < 0.5) {
        const newPosition = generateRandomPosition();
        setPosition(newPosition);
        setIsVisible(true);

        // Random robot state (30% chance to spin)
        const states: ('idle' | 'dance' | 'walk')[] = ['idle', 'idle', 'dance', 'walk'];
        setRobotState(states[Math.floor(Math.random() * states.length)]);

        // Hide after 8-15 seconds
        const hideDelay = 8000 + Math.random() * 7000;
        setTimeout(() => {
          if (!isGameActive) {
            setIsVisible(false);
          }
        }, hideDelay);
      }
    };

    // Check every 10 seconds for testing
    const interval = setInterval(showRobot, 10000);

    // Also show immediately for testing
    setTimeout(showRobot, 1000);

    return () => clearInterval(interval);
  }, [isGameActive, isOnAuthPage, isEarnPointsEnabled, generateRandomPosition]);

  // Auto-hide result message after 3 seconds
  useEffect(() => {
    if (showResult) {
      const timer = setTimeout(() => {
        setShowResult(false);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [showResult]);

  // Hide robot and exit game when on auth page
  useEffect(() => {
    if (isOnAuthPage) {
      setIsVisible(false);
      if (isGameActive) {
        setIsGameActive(false);
        setLightsOn(true);
        setUserGuess(null);
        setActualPosition(null);
        setShowResult(false);
      }
    }
  }, [isOnAuthPage, isGameActive]);

  // Process pending rewards when user logs in
  useEffect(() => {
    const processPendingRewards = async () => {
      if (!user) return;

      const pendingRewards = JSON.parse(localStorage.getItem('pendingDasWosRewards') || '[]');
      if (pendingRewards.length === 0) return;

      console.log(`Processing ${pendingRewards.length} pending rewards for user:`, user);

      let totalAwarded = 0;
      const processedRewards = [];

      for (const reward of pendingRewards) {
        try {
          const response = await fetch('/api/user/daswos-coins/game-reward', {
            method: 'POST',
            credentials: 'include',
            headers: {
              'Content-Type': 'application/json',
            },
          });

          const data = await response.json();

          if (data.success) {
            totalAwarded += reward.amount;
            processedRewards.push(reward);
            console.log('Successfully awarded pending reward:', reward);
          }
        } catch (error) {
          console.error('Error processing pending reward:', reward, error);
        }
      }

      if (totalAwarded > 0) {
        // Remove processed rewards from localStorage
        const remainingRewards = pendingRewards.filter(r => !processedRewards.includes(r));
        localStorage.setItem('pendingDasWosRewards', JSON.stringify(remainingRewards));

        // Show success toast
        toast({
          title: "🎉 Welcome back!",
          description: `You earned ${totalAwarded} DasWos Coin${totalAwarded > 1 ? 's' : ''} from your previous games!`,
          duration: 6000,
        });
      }
    };

    processPendingRewards();
  }, [user, toast]);

  // Initialize cursor position when game starts
  useEffect(() => {
    if (isGameActive && !lightsOn && !userGuess) {
      // Set cursor to center of screen when game starts
      setCursorPosition({
        x: window.innerWidth / 2,
        y: window.innerHeight / 2
      });
    }
  }, [isGameActive, lightsOn, userGuess]);

  // Handle robot click to start game
  const handleRobotClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    console.log('Robot clicked! Starting game...');

    if (isGameActive) return;

    setIsGameActive(true);
    setIsVisible(false);
    setLightsOn(false);
    setUserGuess(null);
    setShowResult(false);

    // Generate actual robot position for the game
    const newActualPosition = generateRandomPosition();
    setActualPosition(newActualPosition);

    console.log('Game started, actual position:', newActualPosition);
  };

  // Handle screen click for guess placement, changing guess, or restarting
  const handleScreenClick = (event: React.MouseEvent) => {
    // Don't handle clicks on control buttons or other UI elements
    const target = event.target as HTMLElement;
    if (target.closest('button') || target.closest('[data-no-guess]')) {
      return;
    }

    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Check if we've reached the guess limit
    if (totalGuesses >= MAX_GUESSES) {
      // Show final message before exiting
      toast({
        title: "Game Over!",
        description: `You've used all ${MAX_GUESSES} guesses. Wait for DasWos to appear again to play another round!`,
        duration: 4000,
      });
      handleExitGame();
      return;
    }

    // If lights are on (results showing), restart game with new guess
    if (lightsOn && showResult) {
      handlePlayAgain();
      // Place the new guess immediately
      setCursorPosition({ x, y });
      setUserGuess({ x, y });
      setTotalGuesses(prev => prev + 1);
      return;
    }

    // Normal guessing phase
    if (!isGameActive) return;

    // Update cursor position and place guess
    setCursorPosition({ x, y });
    setUserGuess({ x, y });
    const newGuessCount = totalGuesses + 1;
    setTotalGuesses(newGuessCount);

    // Show warning when approaching limit
    if (newGuessCount === MAX_GUESSES - 2) {
      toast({
        title: "Almost out of guesses!",
        description: `Only ${MAX_GUESSES - newGuessCount} guesses remaining in this round.`,
        duration: 3000,
      });
    } else if (newGuessCount === MAX_GUESSES - 1) {
      toast({
        title: "Last guess!",
        description: "This is your final guess before the game ends.",
        duration: 3000,
      });
    }
  };

  // Handle mouse movement to update cursor position (during entire game)
  const handleMouseMove = (event: React.MouseEvent) => {
    // Update cursor during entire game (until lights are on)
    if (!isGameActive || lightsOn) return;

    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Update cursor position to follow mouse
    setCursorPosition({ x, y });
  };

  // Handle light switch toggle
  const handleLightSwitch = () => {
    if (!userGuess || !actualPosition) return;

    setLightsOn(true);
    setShowResult(true);

    // Check if guess was correct and award coin
    if (isGuessCorrect()) {
      awardCoin();
    }
  };

  // Handle exit game
  const handleExitGame = () => {
    setIsGameActive(false);
    setLightsOn(true);
    setUserGuess(null);
    setActualPosition(null);
    setShowResult(false);
    setTotalGuesses(0); // Reset guess counter for next game session
  };

  // Handle play again
  const handlePlayAgain = () => {
    setLightsOn(false);
    setUserGuess(null);
    setShowResult(false);

    // Generate new actual position
    const newActualPosition = generateRandomPosition();
    setActualPosition(newActualPosition);
  };

  // Handle manual light toggle (keeps current guess)
  const handleManualLightToggle = () => {
    if (lightsOn) {
      // Turn off lights but keep the current guess position
      setLightsOn(false);
      setShowResult(false);
      // Generate new actual position for next round
      const newActualPosition = generateRandomPosition();
      setActualPosition(newActualPosition);
    } else {
      // Turn on lights to reveal robot
      handleLightSwitch();
    }
  };

  // Auto-restart game after showing results (if enabled)
  React.useEffect(() => {
    if (showResult && lightsOn && autoRestart) {
      const timer = setTimeout(() => {
        // Auto-restart the game after 3 seconds
        handlePlayAgain();
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [showResult, lightsOn, autoRestart]);

  // Auto turn on lights after 1 second of placing guess (if enabled)
  React.useEffect(() => {
    if (userGuess && !lightsOn && autoLights) {
      const timer = setTimeout(() => {
        handleLightSwitch();
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [userGuess, lightsOn, autoLights]);

  // Handle auto-lights setting change during gameplay
  React.useEffect(() => {
    // If auto-lights is turned ON while user has a guess and lights are off
    if (autoLights && userGuess && !lightsOn && !showResult) {
      const timer = setTimeout(() => {
        handleLightSwitch();
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [autoLights]);

  // Handle auto-restart setting change during gameplay
  React.useEffect(() => {
    // If auto-restart is turned ON while results are showing
    if (autoRestart && showResult && lightsOn) {
      const timer = setTimeout(() => {
        handlePlayAgain();
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [autoRestart]);

  // Handle setting changes with proper state management
  const handleAutoLightsChange = (enabled: boolean) => {
    setAutoLights(enabled);
    // If turning ON auto-lights and user has a guess with lights off, trigger auto-lights
    if (enabled && userGuess && !lightsOn && !showResult) {
      setTimeout(() => {
        handleLightSwitch();
      }, 1000);
    }
  };

  const handleAutoRestartChange = (enabled: boolean) => {
    setAutoRestart(enabled);
    // If turning ON auto-restart and results are currently showing, trigger auto-restart
    if (enabled && showResult && lightsOn) {
      setTimeout(() => {
        handlePlayAgain();
      }, 3000);
    }
  };

  // Validate and fix game state when needed
  const validateGameState = () => {
    // If user has a guess but no actual position, generate one
    if (userGuess && !actualPosition) {
      const newActualPosition = generateRandomPosition();
      setActualPosition(newActualPosition);
    }

    // If lights are off, user has a guess, auto-lights is on, but no timer is running
    if (!lightsOn && userGuess && autoLights && !showResult) {
      setTimeout(() => {
        handleLightSwitch();
      }, 1000);
    }
  };





  // Calculate distance between guess and actual position
  const calculateDistance = (): number => {
    if (!userGuess || !actualPosition) return 0;
    const dx = userGuess.x - actualPosition.x;
    const dy = userGuess.y - actualPosition.y;
    return Math.sqrt(dx * dx + dy * dy);
  };

  // Determine if guess was close enough (within 25 pixels for accurate targeting)
  const isGuessCorrect = (): boolean => {
    return calculateDistance() <= 25;
  };

  // Calculate optimal position for result dialog to avoid covering markers
  const getDialogPosition = () => {
    if (!userGuess || !actualPosition) return { top: '50%', left: '50%' };

    const centerY = (userGuess.y + actualPosition.y) / 2;
    const centerX = (userGuess.x + actualPosition.x) / 2;

    // If markers are in the center area, move dialog to top or bottom
    if (centerY > window.innerHeight * 0.3 && centerY < window.innerHeight * 0.7) {
      // Move to top if markers are in center/bottom, bottom if in top
      const dialogY = centerY > window.innerHeight * 0.5 ? '20%' : '80%';
      return { top: dialogY, left: '50%' };
    }

    // If markers are in left/right areas, move dialog to opposite side
    if (centerX < window.innerWidth * 0.3) {
      return { top: '50%', left: '70%' };
    } else if (centerX > window.innerWidth * 0.7) {
      return { top: '50%', left: '30%' };
    }

    // Default center position
    return { top: '50%', left: '50%' };
  };

  // Don't render anything on auth page
  if (isOnAuthPage) {
    return null;
  }

  // Game overlay render
  if (isGameActive) {
    return (
      <>
        <div
          className="fixed inset-0 z-50"
          style={{
            backgroundColor: lightsOn ? 'rgba(0, 0, 0, 0.1)' : 'rgba(0, 0, 0, 0.95)',
            transition: 'background-color 0.5s ease',
            cursor: lightsOn ? 'default' : 'none' // Show normal cursor when lights are on
          }}
          onClick={handleScreenClick}
          onMouseMove={!lightsOn ? handleMouseMove : undefined}
        >


        {/* Instructions - only show for first 2 guesses */}
        {!lightsOn && !userGuess && totalGuesses < 2 && (
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white text-center">
            <p className="text-lg mb-2">Move mouse to position cursor</p>
            <p className="text-sm text-gray-300">Click anywhere to guess where the robot will appear</p>
            <p className="text-xs text-gray-400 mt-2">You have {MAX_GUESSES} guesses total</p>
          </div>
        )}

        {/* Instructions for waiting or manual light switch - only show for first 2 guesses */}
        {!lightsOn && userGuess && totalGuesses < 2 && (
          <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 text-white text-center">
            {autoLights ? (
              <>
                <p className="text-lg mb-2">Lights turning on in 1 second...</p>
                <p className="text-sm text-gray-300">Or click anywhere to change your guess</p>
              </>
            ) : (
              <>
                <p className="text-lg mb-4">Click the light switch to reveal the robot</p>
                <button
                  onClick={handleLightSwitch}
                  className="bg-black text-white hover:bg-gray-700 border border-black dark:text-white px-6 py-3 rounded font-medium text-base transition-colors flex items-center gap-2 mx-auto"
                >
                  💡 Turn On Lights
                </button>
                <p className="text-sm text-gray-300 mt-2">Or click anywhere to change your guess</p>
              </>
            )}
          </div>
        )}

        {/* Instructions for auto-restart or manual restart - only show for first 2 guesses */}
        {lightsOn && showResult && totalGuesses < 2 && (
          <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 text-white text-center">
            {autoRestart ? (
              <>
                <p className="text-lg mb-2">Next round starting automatically...</p>
                <p className="text-sm text-gray-300">Or click anywhere to place your next guess immediately</p>
              </>
            ) : (
              <>
                <p className="text-lg mb-2">Click anywhere to start next round</p>
                <p className="text-sm text-gray-300">Or use the options button to enable auto-restart</p>
              </>
            )}
          </div>
        )}

        {/* Session coin counter and guess counter - DasWos Style */}
        <div className="absolute top-4 left-4 z-60 flex flex-col gap-2" data-no-guess>
          <div className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-black dark:text-white px-3 py-2 rounded-lg shadow-lg cursor-default">
            <span className="text-sm font-medium">{sessionCoins}</span>
          </div>
          <div className={`bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-black dark:text-white px-3 py-2 rounded-lg shadow-lg cursor-default ${totalGuesses >= MAX_GUESSES - 2 ? 'border-red-400 bg-red-50 dark:bg-red-900' : ''}`}>
            <span className={`text-sm font-medium ${totalGuesses >= MAX_GUESSES - 2 ? 'text-red-600 dark:text-red-300' : ''}`}>
              {MAX_GUESSES - totalGuesses} left
            </span>
          </div>
        </div>

        {/* Game controls - Exit and Options buttons - DasWos Style */}
        <div className="absolute top-4 right-4 flex gap-2 z-60" data-no-guess>
          {/* Light switch button - show when auto-lights is off */}
          {!autoLights && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleManualLightToggle();
              }}
              className={`w-10 h-10 ${lightsOn ? 'bg-gray-600 hover:bg-gray-700' : 'bg-yellow-500 hover:bg-yellow-600'} border ${lightsOn ? 'border-gray-500' : 'border-yellow-400'} text-${lightsOn ? 'white' : 'black'} rounded-lg flex items-center justify-center text-lg transition-colors shadow-lg cursor-pointer`}
              title={lightsOn ? "Turn Off Lights (New Round)" : "Turn On Lights"}
              data-no-guess
            >
              {lightsOn ? '🌙' : '💡'}
            </button>
          )}

          {/* Options button */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowGameOptions(true);
            }}
            className="w-10 h-10 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-black dark:text-white rounded-lg flex items-center justify-center text-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors shadow-lg cursor-pointer"
            title="Game Options"
            data-no-guess
          >
            ⚙
          </button>

          {/* Exit button */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleExitGame();
            }}
            className="w-10 h-10 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-black dark:text-white rounded-lg flex items-center justify-center text-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors shadow-lg cursor-pointer"
            title="Exit Game"
            data-no-guess
          >
            ×
          </button>
        </div>

        {/* Custom crosshair cursor - always visible during game */}
        <div
          className="absolute transform -translate-x-1/2 -translate-y-1/2 z-50 pointer-events-none"
          style={{
            left: cursorPosition.x,
            top: cursorPosition.y,
          }}
        >
          {/* Crosshair lines */}
          <div className="relative">
            {/* Vertical line */}
            <div className="absolute w-0.5 h-8 bg-white shadow-lg" style={{ left: '-1px', top: '-16px' }}></div>
            {/* Horizontal line */}
            <div className="absolute h-0.5 w-8 bg-white shadow-lg" style={{ top: '-1px', left: '-16px' }}></div>
            {/* Center dot */}
            <div className="absolute w-1.5 h-1.5 bg-white rounded-full shadow-lg" style={{ left: '-3px', top: '-3px' }}></div>
          </div>
        </div>

        {/* User's guess marker - target style */}
        {userGuess && (
          <div
            className="absolute transform -translate-x-1/2 -translate-y-1/2 z-50"
            style={{ left: userGuess.x, top: userGuess.y }}
          >
            {/* Target rings - smaller and more precise */}
            <div className="relative">
              {/* Winning area boundary - 25px radius (50px diameter) */}
              <div className="absolute w-12 h-12 border-2 border-yellow-400 rounded-full opacity-40" style={{ left: '-24px', top: '-24px' }}></div>
              {/* Outer ring - red */}
              <div className="absolute w-10 h-10 border-2 border-red-500 rounded-full" style={{ left: '-20px', top: '-20px' }}></div>
              {/* Middle ring - white (precise target zone) */}
              <div className="absolute w-6 h-6 border-2 border-white rounded-full" style={{ left: '-12px', top: '-12px' }}></div>
              {/* Inner ring - red (bullseye) */}
              <div className="absolute w-3 h-3 border-2 border-red-500 rounded-full" style={{ left: '-6px', top: '-6px' }}></div>
              {/* Center dot - white */}
              <div className="absolute w-1 h-1 bg-white rounded-full shadow-lg" style={{ left: '-2px', top: '-2px' }}></div>
            </div>
            {/* Label */}
            <div className="absolute -top-16 left-1/2 transform -translate-x-1/2 bg-white text-black px-2 py-1 rounded border border-black text-xs font-medium whitespace-nowrap">
              Your Guess
            </div>
          </div>
        )}



        {/* Actual robot position (only visible when lights are on) */}
        {lightsOn && actualPosition && (
          <div
            className="absolute transform -translate-x-1/2 -translate-y-1/2 z-50"
            style={{ left: actualPosition.x, top: actualPosition.y }}
          >
            {/* Robot image */}
            <img
              src="/daswos_redesign_correct_logo_simple.png"
              alt="DasWos Robot"
              className="w-20 h-20 object-contain"
              onError={(e) => {
                console.log('Game image failed to load from:', e.currentTarget.src);
              }}
            />
          </div>
        )}

        {/* Result display - DasWos Style */}
        {showResult && userGuess && actualPosition && (
          <div className="absolute top-20 left-1/2 transform -translate-x-1/2 z-50" data-no-guess>
            <div className="bg-white dark:bg-[#222222] border border-gray-300 dark:border-gray-600 p-3 rounded-lg text-center backdrop-blur-sm max-w-xs shadow-lg cursor-default">
              <h3 className="text-base font-semibold mb-2 text-black dark:text-white">
                {isGuessCorrect() ? 'Excellent!' : 'Close, but not quite!'}
              </h3>
              <p className="text-sm mb-1 text-gray-800 dark:text-gray-200">
                Distance: <span className="font-medium">{Math.round(calculateDistance())} pixels</span>
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {isGuessCorrect()
                  ? user
                    ? 'You found the robot and earned 1 DasWos Coin!'
                    : 'You found the robot! Your reward is saved - sign up to collect it!'
                  : 'Try to get within the target rings next time!'
                }
              </p>
            </div>
          </div>
        )}
        </div>

        {/* Game Options Dialog - DasWos Style */}
        {showGameOptions && (
          <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-md flex items-center justify-center z-[10000]">
            <div className="bg-white dark:bg-[#222222] border border-gray-300 dark:border-gray-600 p-6 rounded-lg max-w-md mx-4 shadow-lg">
              <h3 className="text-lg font-semibold mb-4 text-center text-black dark:text-white">Game Options</h3>

              {/* Auto-restart toggle */}
              <div className="mb-4">
                <label className="flex items-center justify-between cursor-pointer">
                  <span className="text-sm text-gray-800 dark:text-gray-200">Auto-restart next round</span>
                  <div className="relative">
                    <input
                      type="checkbox"
                      checked={autoRestart}
                      onChange={(e) => handleAutoRestartChange(e.target.checked)}
                      className="sr-only"
                    />
                    <div className={`w-10 h-5 rounded-full transition-colors border ${autoRestart ? 'bg-black dark:bg-white border-black dark:border-white' : 'bg-gray-300 dark:bg-gray-600 border-gray-400 dark:border-gray-500'}`}>
                      <div className={`w-4 h-4 bg-white dark:bg-black rounded-full transition-transform transform ${autoRestart ? 'translate-x-5' : 'translate-x-0'} mt-0.5 ml-0.5`}></div>
                    </div>
                  </div>
                </label>
                <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">Automatically start new round after 3 seconds</p>
              </div>

              {/* Auto-lights toggle */}
              <div className="mb-6">
                <label className="flex items-center justify-between cursor-pointer">
                  <span className="text-sm text-gray-800 dark:text-gray-200">Auto turn on lights</span>
                  <div className="relative">
                    <input
                      type="checkbox"
                      checked={autoLights}
                      onChange={(e) => handleAutoLightsChange(e.target.checked)}
                      className="sr-only"
                    />
                    <div className={`w-10 h-5 rounded-full transition-colors border ${autoLights ? 'bg-black dark:bg-white border-black dark:border-white' : 'bg-gray-300 dark:bg-gray-600 border-gray-400 dark:border-gray-500'}`}>
                      <div className={`w-4 h-4 bg-white dark:bg-black rounded-full transition-transform transform ${autoLights ? 'translate-x-5' : 'translate-x-0'} mt-0.5 ml-0.5`}></div>
                    </div>
                  </div>
                </label>
                <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">Automatically reveal robot after 1 second</p>
              </div>

              {/* Close button */}
              <div className="flex justify-center">
                <button
                  onClick={() => {
                    setShowGameOptions(false);
                    // Validate and fix game state after settings change
                    setTimeout(() => validateGameState(), 100);
                  }}
                  className="bg-black text-white hover:bg-gray-700 border border-black dark:text-white px-6 py-2 rounded text-sm font-medium transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Congratulations Dialog - DasWos Style */}
        {showCongratulations && (
          <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-md flex items-center justify-center z-[10001]">
            <div className="bg-white dark:bg-[#222222] border border-gray-300 dark:border-gray-600 p-8 rounded-lg max-w-md mx-4 text-center shadow-lg">
              <div className="text-4xl mb-4">🎉</div>
              <h3 className="text-xl font-semibold mb-4 text-black dark:text-white">Congratulations!</h3>
              <p className="text-base mb-2 text-gray-800 dark:text-gray-200">Perfect shot! You found the robot!</p>
              <p className="text-sm mb-4 text-gray-700 dark:text-gray-300">
                {user
                  ? `You earned 1 DasWos Coin!`
                  : `Your reward is saved! Sign up to collect your coins!`
                }
              </p>
              <p className="text-xs mb-6 text-gray-600 dark:text-gray-400">
                Session coins earned: {sessionCoins}
              </p>
              <button
                onClick={() => setShowCongratulations(false)}
                className="bg-black text-white hover:bg-gray-700 border border-black dark:text-white px-6 py-2 rounded text-sm font-medium transition-colors"
              >
                Continue Playing
              </button>
            </div>
          </div>
        )}

        {/* Signup Prompt Dialog */}
        <SignupPromptDialog
          isOpen={showSignupPrompt}
          onSignup={() => {
            setShowSignupPrompt(false);
            setLocation('/auth');
          }}
          onDecline={() => {
            setShowSignupPrompt(false);
          }}
        />
      </>
    );
  }

  // Regular robot display
  if (!isVisible) return null;

  return (
    <div
      className={`fixed cursor-pointer transition-all duration-500 hover:scale-110 pointer-events-auto ${className}`}
      style={{
        left: position.x,
        top: position.y,
        transform: 'translate(-50%, -50%)',
        pointerEvents: 'auto',
        zIndex: 1
      }}
      onClick={handleRobotClick}
      onMouseDown={(e) => e.stopPropagation()}
    >
      {/* Robot image */}
      <img
        src="/daswos_redesign_correct_logo_simple.png"
        alt="DasWos Robot"
        className={`w-20 h-20 object-contain ${
          robotState === 'dance' ? 'animate-spin' :
          robotState === 'walk' ? 'animate-pulse' :
          'animate-none'
        }`}
        onError={(e) => {
          console.log('Image failed to load from:', e.currentTarget.src);
        }}
        onLoad={() => {
          console.log('Robot image loaded successfully!');
        }}
      />
    </div>
  );
};

export default DaswosRobot;

import React, { useState, useEffect, useCallback } from 'react';

interface Position {
  x: number;
  y: number;
}

interface DaswosRobotProps {
  className?: string;
}

const DaswosRobot: React.FC<DaswosRobotProps> = ({ className = '' }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState<Position>({ x: 0, y: 0 });
  const [robotState, setRobotState] = useState<'idle' | 'dance' | 'walk'>('idle');

  // Game state
  const [isGameActive, setIsGameActive] = useState(false);
  const [userGuess, setUserGuess] = useState<Position | null>(null);
  const [actualPosition, setActualPosition] = useState<Position | null>(null);
  const [showResult, setShowResult] = useState(false);
  const [lightsOn, setLightsOn] = useState(true);

  // Generate random position within screen bounds
  const generateRandomPosition = useCallback((): Position => {
    const margin = 100;
    const x = Math.random() * (window.innerWidth - 2 * margin) + margin;
    const y = Math.random() * (window.innerHeight - 2 * margin) + margin;
    return { x, y };
  }, []);

  // Show robot randomly
  useEffect(() => {
    if (isGameActive) return; // Don't show random robot during game

    const showRobot = () => {
      // Random chance to show robot (15% chance every 20 seconds)
      if (Math.random() < 0.15) {
        const newPosition = generateRandomPosition();
        setPosition(newPosition);
        setIsVisible(true);

        // Random robot state
        const states: ('idle' | 'dance' | 'walk')[] = ['idle', 'dance', 'walk'];
        setRobotState(states[Math.floor(Math.random() * states.length)]);

        // Hide after 8-15 seconds
        const hideDelay = 8000 + Math.random() * 7000;
        setTimeout(() => {
          if (!isGameActive) {
            setIsVisible(false);
          }
        }, hideDelay);
      }
    };

    // Check every 20 seconds
    const interval = setInterval(showRobot, 20000);

    // Also show immediately with lower chance
    setTimeout(showRobot, 3000);

    return () => clearInterval(interval);
  }, [isGameActive, generateRandomPosition]);

  // Handle robot click to start game
  const handleRobotClick = () => {
    if (isGameActive) return;

    setIsGameActive(true);
    setIsVisible(false);
    setLightsOn(false);
    setUserGuess(null);
    setShowResult(false);

    // Generate actual robot position for the game
    const newActualPosition = generateRandomPosition();
    setActualPosition(newActualPosition);
  };

  // Handle screen click for guess placement
  const handleScreenClick = (event: React.MouseEvent) => {
    if (!isGameActive || lightsOn || userGuess) return;

    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    setUserGuess({ x, y });
  };

  // Handle light switch toggle
  const handleLightSwitch = () => {
    if (!userGuess || !actualPosition) return;

    setLightsOn(true);
    setShowResult(true);
  };

  // Handle exit game
  const handleExitGame = () => {
    setIsGameActive(false);
    setLightsOn(true);
    setUserGuess(null);
    setActualPosition(null);
    setShowResult(false);
  };

  // Handle play again
  const handlePlayAgain = () => {
    setLightsOn(false);
    setUserGuess(null);
    setShowResult(false);

    // Generate new actual position
    const newActualPosition = generateRandomPosition();
    setActualPosition(newActualPosition);
  };

  // Calculate distance between guess and actual position
  const calculateDistance = (): number => {
    if (!userGuess || !actualPosition) return 0;
    const dx = userGuess.x - actualPosition.x;
    const dy = userGuess.y - actualPosition.y;
    return Math.sqrt(dx * dx + dy * dy);
  };

  // Determine if guess was close enough (within 100 pixels)
  const isGuessCorrect = (): boolean => {
    return calculateDistance() <= 100;
  };

  // Game overlay render
  if (isGameActive) {
    return (
      <div
        className="fixed inset-0 z-50 cursor-crosshair"
        style={{
          backgroundColor: lightsOn ? 'rgba(0, 0, 0, 0.1)' : 'rgba(0, 0, 0, 0.95)',
          transition: 'background-color 0.5s ease'
        }}
        onClick={handleScreenClick}
      >
        {/* Exit button */}
        <button
          onClick={handleExitGame}
          className="absolute top-4 right-4 w-12 h-12 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-xl font-bold z-60 transition-colors"
        >
          ×
        </button>

        {/* Instructions */}
        {!lightsOn && !userGuess && (
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white text-center">
            <h2 className="text-3xl font-bold mb-4">🔍 Find the DasWos Robot!</h2>
            <p className="text-lg">Click anywhere to guess where the robot will appear</p>
          </div>
        )}

        {/* User's guess marker */}
        {userGuess && (
          <div
            className="absolute w-8 h-8 bg-yellow-400 rounded-full border-4 border-yellow-600 transform -translate-x-1/2 -translate-y-1/2 z-50"
            style={{ left: userGuess.x, top: userGuess.y }}
          >
            <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 text-yellow-400 text-xs font-bold whitespace-nowrap">
              Your Guess
            </div>
          </div>
        )}

        {/* Light switch button */}
        {userGuess && !lightsOn && (
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
            <button
              onClick={handleLightSwitch}
              className="bg-yellow-500 hover:bg-yellow-600 text-black px-8 py-4 rounded-lg font-bold text-lg transition-colors flex items-center gap-2"
            >
              💡 Turn On Lights
            </button>
          </div>
        )}

        {/* Actual robot position (only visible when lights are on) */}
        {lightsOn && actualPosition && (
          <div
            className="absolute transform -translate-x-1/2 -translate-y-1/2 z-50"
            style={{ left: actualPosition.x, top: actualPosition.y }}
          >
            {/* Robot image */}
            <div className="w-20 h-20 relative">
              <img
                src="/assets/daswos-robot.png"
                alt="DasWos Robot"
                className="w-full h-full object-contain"
                onError={(e) => {
                  // Fallback to emoji if image fails to load
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.nextElementSibling!.style.display = 'flex';
                }}
              />
              <div className="w-full h-full bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-2xl hidden">
                🤖
              </div>
            </div>
          </div>
        )}

        {/* Result display */}
        {showResult && userGuess && actualPosition && (
          <div className="absolute top-8 left-1/2 transform -translate-x-1/2 bg-white rounded-lg p-6 shadow-xl text-center max-w-md">
            <h3 className="text-2xl font-bold mb-4">
              {isGuessCorrect() ? '🎉 Great Guess!' : '🎯 Close, but not quite!'}
            </h3>
            <p className="text-lg mb-2">
              Distance: <span className="font-bold">{Math.round(calculateDistance())} pixels</span>
            </p>
            <p className="text-sm text-gray-600 mb-4">
              {isGuessCorrect() ? 'You found the robot within 100 pixels!' : 'Try to get within 100 pixels next time!'}
            </p>
            <div className="flex gap-3 justify-center">
              <button
                onClick={handlePlayAgain}
                className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg font-bold transition-colors"
              >
                🎮 Play Again
              </button>
              <button
                onClick={handleExitGame}
                className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-bold transition-colors"
              >
                🚪 Exit Game
              </button>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Regular robot display
  if (!isVisible) return null;

  return (
    <div
      className={`fixed z-50 cursor-pointer transition-all duration-500 hover:scale-110 ${className}`}
      style={{
        left: position.x,
        top: position.y,
        transform: 'translate(-50%, -50%)'
      }}
      onClick={handleRobotClick}
    >
      {/* Robot image */}
      <div className={`w-16 h-16 relative ${
        robotState === 'dance' ? 'animate-bounce' :
        robotState === 'walk' ? 'animate-pulse' :
        'animate-none'
      }`}>
        <img
          src="/assets/daswos-robot.png"
          alt="DasWos Robot"
          className="w-full h-full object-contain"
          onError={(e) => {
            // Fallback to emoji if image fails to load
            e.currentTarget.style.display = 'none';
            e.currentTarget.nextElementSibling!.style.display = 'flex';
          }}
        />
        <div className="w-full h-full bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-xl hidden">
          🤖
        </div>
      </div>

      {/* Speech bubble */}
      <div className="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-white rounded-lg px-3 py-1 text-xs shadow-lg border">
        {robotState === 'dance' ? '💃 Dancing!' :
         robotState === 'walk' ? '🚶 Walking!' :
         '👋 Click me!'}
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-white"></div>
      </div>
    </div>
  );
};

export default DaswosRobot;

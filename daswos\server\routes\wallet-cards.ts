import express from 'express';
import { eq, and } from 'drizzle-orm';
import { db } from '../db';
import { users, walletCards } from '@shared/schema1';
import { isAuthenticated } from '../middleware/auth';
import { createClient } from '@supabase/supabase-js';

const router = express.Router();

// Wallet database configuration
const WALLET_DATABASE_CONFIG = {
  SUPABASE_URL: 'https://mjyaqqsxhkqyzqufpxzl.supabase.co',
  SUPABASE_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1qeWFxcXN4aGtxeXpxdWZweHpsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODYyMzU4MiwiZXhwIjoyMDY0MTk5NTgyfQ.1LBqR-7c8UdBa8koW69nQrYSr_Lm1aErRUgoRteFFVs'
};

// Initialize Supabase client for wallet database
const walletSupabase = createClient(
  WALLET_DATABASE_CONFIG.SUPABASE_URL,
  WALLET_DATABASE_CONFIG.SUPABASE_KEY
);

/**
 * GET /api/wallet/cards
 * Get all cards for a wallet
 */
router.get('/cards', isAuthenticated, async (req, res) => {
  try {
    const { walletId } = req.query;
    const userId = req.user?.id;

    console.log(`🔍 GET /api/wallet/cards called with:`, {
      walletId,
      userId,
      walletIdType: typeof walletId,
      walletIdLength: walletId?.length
    });

    if (!walletId || !userId) {
      return res.status(400).json({
        success: false,
        error: 'Missing walletId or user authentication'
      });
    }

    // Check if user has access to this wallet
    // Users can access wallets through:
    // 1. Their primary wallet (users.walletId)
    // 2. Any wallet they have cards in (wallet_cards table)
    // 3. Any wallet they're connected to (wallet_connections table)

    const [user] = await db
      .select({ walletId: users.walletId })
      .from(users)
      .where(eq(users.id, userId));

    // Check if this is the user's primary wallet OR if they have cards in this wallet
    const hasAccess = user?.walletId === walletId ||
      await db
        .select({ count: count() })
        .from(walletCards)
        .where(and(
          eq(walletCards.userId, userId),
          eq(walletCards.walletId, walletId as string)
        ))
        .then(result => result[0]?.count > 0);

    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        error: 'User does not have access to this wallet'
      });
    }

    // Get all cards for this wallet from main database
    console.log(`🔍 Querying cards for userId: ${userId}, walletId: ${walletId}`);
    const cards = await db
      .select()
      .from(walletCards)
      .where(and(
        eq(walletCards.userId, userId),
        eq(walletCards.walletId, walletId as string)
      ))
      .orderBy(walletCards.createdAt);

    console.log(`🔍 Found ${cards.length} cards:`, cards.map(c => ({
      cardId: c.cardId,
      cardName: c.cardName,
      isPrimary: c.isPrimary,
      balance: c.balance
    })));

    // Get user's active card ID
    const [userData] = await db
      .select({ activeCardId: users.activeCardId })
      .from(users)
      .where(eq(users.id, userId));

    // Mark which card is currently active
    const cardsWithActiveStatus = cards.map(card => ({
      ...card,
      isCurrentlyActive: userData?.activeCardId === card.cardId
    }));

    console.log(`📋 Card retrieval for user ${userId}, wallet ${walletId}:`);
    console.log(`📋 User primary wallet: ${user?.walletId}`);
    console.log(`📋 Retrieved ${cards.length} cards for wallet ${walletId}, active card: ${userData?.activeCardId}`);
    console.log(`📋 Cards details:`, cardsWithActiveStatus.map(c => ({
      cardId: c.cardId,
      cardName: c.cardName,
      balance: c.balance,
      isPrimary: c.isPrimary,
      isCurrentlyActive: c.isCurrentlyActive
    })));

    // Also log the raw query for debugging
    console.log(`📋 Query used: userId=${userId}, walletId=${walletId}`);

    res.json({
      success: true,
      cards: cardsWithActiveStatus,
      activeCardId: userData?.activeCardId
    });

  } catch (error) {
    console.error('Error fetching wallet cards:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * POST /api/wallet/cards/create
 * Create a new card for a wallet
 */
router.post('/cards/create', isAuthenticated, async (req, res) => {
  try {
    const { walletId, cardName, isSafeCard } = req.body;
    const userId = req.user?.id;

    if (!walletId || !userId) {
      return res.status(400).json({
        success: false,
        error: 'Missing walletId or user authentication'
      });
    }

    // Check if user has access to this wallet
    const [user] = await db
      .select({ walletId: users.walletId })
      .from(users)
      .where(eq(users.id, userId));

    // Check if this is the user's primary wallet OR if they have cards in this wallet
    const hasAccess = user?.walletId === walletId ||
      await db
        .select({ count: count() })
        .from(walletCards)
        .where(and(
          eq(walletCards.userId, userId),
          eq(walletCards.walletId, walletId as string)
        ))
        .then(result => result[0]?.count > 0);

    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        error: 'User does not have access to this wallet'
      });
    }

    // Check current card count
    const existingCards = await db
      .select()
      .from(walletCards)
      .where(and(
        eq(walletCards.userId, userId),
        eq(walletCards.walletId, walletId)
      ));

    if (existingCards.length >= 5) {
      return res.status(400).json({
        success: false,
        error: 'Maximum of 5 cards per wallet allowed'
      });
    }

    // Generate unique card ID
    const cardId = `card-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const finalCardName = cardName || `Card ${existingCards.length + 1}`;

    console.log(`💳 Creating new card for user ${userId}, wallet ${walletId}:`, {
      cardId,
      cardName: finalCardName,
      isFirstCard: existingCards.length === 0,
      existingCardCount: existingCards.length,
      isSafeCard: isSafeCard || false
    });

    const isFirstCard = existingCards.length === 0;

    // Create new card in main database (daswos-app)
    const [newCard] = await db
      .insert(walletCards)
      .values({
        userId,
        walletId,
        cardId,
        cardName: finalCardName,
        balance: 0,
        totalEarned: 0,
        totalSpent: 0,
        isPrimary: isFirstCard, // First card is primary
        isActive: true,
        isSafeCard: isSafeCard || false, // Safe card flag for safety features
        createdAt: new Date()
      })
      .returning();

    console.log(`✅ Card created in main database:`, newCard);

    // Also create the card in the wallet database for consistency
    try {
      const { data: walletCard, error: walletCardError } = await walletSupabase
        .from('wallet_cards')
        .insert({
          user_id: userId,
          wallet_id: walletId,
          card_id: cardId,
          card_name: finalCardName,
          balance: 0,
          total_earned: 0,
          total_spent: 0,
          is_primary: isFirstCard,
          is_active: true,
          is_safe_card: isSafeCard || false,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (walletCardError) {
        console.error('❌ Failed to create card in wallet database:', walletCardError);
        // Don't fail the main operation, but log the error
      } else {
        console.log(`✅ Card also created in wallet database:`, walletCard);
      }
    } catch (walletDbError) {
      console.error('❌ Error creating card in wallet database:', walletDbError);
      // Continue with main database card creation
    }

    // If this is the first card, set it as the active card for the user
    if (isFirstCard) {
      try {
        await db
          .update(users)
          .set({ activeCardId: cardId })
          .where(eq(users.id, userId));

        console.log(`✅ Set card ${cardId} as active card for user ${userId}`);
      } catch (activeCardError) {
        console.error('❌ Failed to set active card:', activeCardError);
        // Don't fail the operation, but log the error
      }
    }

    res.json({
      success: true,
      card: newCard,
      isFirstCard,
      setAsActive: isFirstCard
    });

  } catch (error) {
    console.error('Error creating wallet card:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * PUT /api/wallet/cards/set-active
 * Set the active card for a user
 */
router.put('/cards/set-active', isAuthenticated, async (req, res) => {
  try {
    const { cardId } = req.body;
    const userId = req.user?.id;

    if (!cardId || !userId) {
      return res.status(400).json({
        success: false,
        error: 'Missing cardId or user authentication'
      });
    }

    // Verify user owns this card
    const [card] = await db
      .select()
      .from(walletCards)
      .where(and(
        eq(walletCards.userId, userId),
        eq(walletCards.cardId, cardId)
      ));

    if (!card) {
      return res.status(404).json({
        success: false,
        error: 'Card not found or does not belong to user'
      });
    }

    // Update user's active card
    await db
      .update(users)
      .set({ activeCardId: cardId })
      .where(eq(users.id, userId));

    res.json({
      success: true,
      message: 'Active card updated successfully',
      activeCardId: cardId
    });

  } catch (error) {
    console.error('Error setting active card:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * PUT /api/wallet/cards/update-name
 * Update a card's name
 */
router.put('/cards/update-name', isAuthenticated, async (req, res) => {
  try {
    const { cardId, cardName } = req.body;
    const userId = req.user?.id;

    if (!cardId || !cardName || !userId) {
      return res.status(400).json({
        success: false,
        error: 'Missing cardId, cardName, or user authentication'
      });
    }

    // Verify user owns this card
    const [card] = await db
      .select()
      .from(walletCards)
      .where(and(
        eq(walletCards.userId, userId),
        eq(walletCards.cardId, cardId)
      ));

    if (!card) {
      return res.status(404).json({
        success: false,
        error: 'Card not found or does not belong to user'
      });
    }

    // Update card name
    await db
      .update(walletCards)
      .set({ 
        cardName: cardName,
        updatedAt: new Date()
      })
      .where(and(
        eq(walletCards.userId, userId),
        eq(walletCards.cardId, cardId)
      ));

    res.json({
      success: true,
      message: 'Card name updated successfully'
    });

  } catch (error) {
    console.error('Error updating card name:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * POST /api/wallet/cards/add-funds
 * Add funds to a specific card
 */
router.post('/cards/add-funds', isAuthenticated, async (req, res) => {
  try {
    const { cardId, amount, description = 'Funds added' } = req.body;
    const userId = req.user?.id;

    if (!cardId || !amount || !userId) {
      return res.status(400).json({
        success: false,
        error: 'Missing cardId, amount, or user authentication'
      });
    }

    if (amount <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Amount must be positive'
      });
    }

    // Verify user owns this card
    const [card] = await db
      .select()
      .from(walletCards)
      .where(and(
        eq(walletCards.userId, userId),
        eq(walletCards.cardId, cardId)
      ));

    if (!card) {
      return res.status(404).json({
        success: false,
        error: 'Card not found or does not belong to user'
      });
    }

    // Update card balance in main database
    const [updatedCard] = await db
      .update(walletCards)
      .set({
        balance: card.balance + amount,
        totalEarned: card.totalEarned + amount,
        lastTransactionAt: new Date(),
        updatedAt: new Date()
      })
      .where(and(
        eq(walletCards.userId, userId),
        eq(walletCards.cardId, cardId)
      ))
      .returning();

    // Also update in wallet database
    try {
      const { error: walletUpdateError } = await walletSupabase
        .from('wallet_cards')
        .update({
          balance: updatedCard.balance,
          total_earned: updatedCard.totalEarned,
          last_transaction_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('card_id', cardId)
        .eq('user_id', userId);

      if (walletUpdateError) {
        console.error('❌ Failed to update card in wallet database:', walletUpdateError);
      }
    } catch (walletDbError) {
      console.error('❌ Error updating card in wallet database:', walletDbError);
    }

    // Calculate new total balance across all user's cards
    const allUserCards = await db
      .select({ balance: walletCards.balance })
      .from(walletCards)
      .where(eq(walletCards.userId, userId));

    const totalBalance = allUserCards.reduce((sum, card) => sum + card.balance, 0);

    // Update user's total DasWos coins balance
    await db
      .update(users)
      .set({ dasWosCoinsBalance: totalBalance })
      .where(eq(users.id, userId));

    console.log(`💰 Added ${amount} coins to card ${cardId} for user ${userId}. New card balance: ${updatedCard.balance}, Total balance: ${totalBalance}`);

    res.json({
      success: true,
      card: updatedCard,
      totalBalance,
      message: `Successfully added ${amount} coins to ${card.cardName}`
    });

  } catch (error) {
    console.error('Error adding funds to card:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * PUT /api/wallet/cards/update-safe-status
 * Update a card's safe card status
 */
router.put('/cards/update-safe-status', isAuthenticated, async (req, res) => {
  try {
    const { cardId, isSafeCard } = req.body;
    const userId = req.user?.id;

    if (!cardId || typeof isSafeCard !== 'boolean' || !userId) {
      return res.status(400).json({
        success: false,
        error: 'Missing cardId, isSafeCard boolean, or user authentication'
      });
    }

    // Verify user owns this card
    const [card] = await db
      .select()
      .from(walletCards)
      .where(and(
        eq(walletCards.userId, userId),
        eq(walletCards.cardId, cardId)
      ));

    if (!card) {
      return res.status(404).json({
        success: false,
        error: 'Card not found or does not belong to user'
      });
    }

    // Update card safe status in main database
    const [updatedCard] = await db
      .update(walletCards)
      .set({
        isSafeCard: isSafeCard,
        updatedAt: new Date()
      })
      .where(and(
        eq(walletCards.userId, userId),
        eq(walletCards.cardId, cardId)
      ))
      .returning();

    // Also update in wallet database
    try {
      const { error: walletUpdateError } = await walletSupabase
        .from('wallet_cards')
        .update({
          is_safe_card: isSafeCard,
          updated_at: new Date().toISOString()
        })
        .eq('card_id', cardId)
        .eq('user_id', userId);

      if (walletUpdateError) {
        console.error('❌ Failed to update card safe status in wallet database:', walletUpdateError);
      }
    } catch (walletDbError) {
      console.error('❌ Error updating card safe status in wallet database:', walletDbError);
    }

    console.log(`🛡️ Updated card ${cardId} safe status to ${isSafeCard} for user ${userId}`);

    res.json({
      success: true,
      card: updatedCard,
      message: `Successfully updated ${card.cardName} safe card status to ${isSafeCard}`
    });

  } catch (error) {
    console.error('Error updating card safe status:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * GET /api/wallet/cards/debug
 * Debug endpoint to see all cards for a user
 */
router.get('/cards/debug', isAuthenticated, async (req, res) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User authentication required'
      });
    }

    // Get all cards for this user regardless of wallet
    const allCards = await db
      .select()
      .from(walletCards)
      .where(eq(walletCards.userId, userId))
      .orderBy(walletCards.createdAt);

    // Get user info
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, userId));

    console.log(`🔍 DEBUG: User ${userId} has ${allCards.length} total cards`);
    console.log(`🔍 DEBUG: User primary wallet: ${user?.walletId}`);
    console.log(`🔍 DEBUG: User active card: ${user?.activeCardId}`);
    console.log(`🔍 DEBUG: All cards:`, allCards.map(c => ({
      id: c.id,
      walletId: c.walletId,
      cardId: c.cardId,
      cardName: c.cardName,
      balance: c.balance,
      isPrimary: c.isPrimary,
      isActive: c.isActive
    })));

    res.json({
      success: true,
      userId,
      userPrimaryWallet: user?.walletId,
      userActiveCard: user?.activeCardId,
      totalCards: allCards.length,
      cards: allCards
    });

  } catch (error) {
    console.error('Error in debug endpoint:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

export default router;

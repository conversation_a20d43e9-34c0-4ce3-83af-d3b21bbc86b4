import React from 'react';
import { Button } from '@/components/ui/button';

interface SignupPromptDialogProps {
  isOpen: boolean;
  onSignup: () => void;
  onDecline: () => void;
}

const SignupPromptDialog: React.FC<SignupPromptDialogProps> = ({
  isOpen,
  onSignup,
  onDecline
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center">
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" />
      
      {/* Dialog */}
      <div className="relative bg-gray-700/90 backdrop-blur-sm rounded p-6 text-center w-80 h-48 text-white flex flex-col justify-center">
        <h3 className="text-2xl font-medium mb-4 text-white">
          Earn DasWos Coins!
        </h3>
        <p className="text-lg mb-2 text-white">
          You found the robot! Sign up to collect your coin.
        </p>
        <p className="text-sm text-gray-300 mb-6">
          Your reward will be waiting when you create an account!
        </p>
        <div className="flex gap-3 justify-center">
          <Button
            onClick={onSignup}
            className="bg-black hover:bg-gray-800 text-white px-6 py-2 rounded font-medium transition-colors"
          >
            Sign Up
          </Button>
          <Button
            onClick={onDecline}
            className="bg-gray-600 hover:bg-gray-500 text-white px-6 py-2 rounded font-medium transition-colors"
          >
            No Thanks
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SignupPromptDialog;

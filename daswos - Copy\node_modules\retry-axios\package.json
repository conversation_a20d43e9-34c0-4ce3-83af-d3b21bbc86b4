{"name": "retry-axios", "version": "2.6.0", "description": "Retry HTTP requests with Axios.", "source": "src/index.ts", "main": "dist/index.js", "module": "dist/index.module.js", "unpkg": "dist/index.umd.js", "types": "dist/src/index.d.ts", "engines": {"node": ">=10.7.0"}, "repository": {"type": "git", "url": "https://github.com/JustinBeckwith/retry-axios.git"}, "scripts": {"lint": "gts check", "clean": "gts clean", "fix": "gts fix", "compile": "tsc --target ES5 --module CommonJS", "build-web": "microbundle", "umd": "rm -rf umd && babel build/src -d umd --source-maps", "test": "c8 mocha build/test", "prepare": "npm run build-web", "pretest": "npm run compile", "license-check": "jsgl --local ."}, "keywords": ["axios", "retry"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "peerDependencies": {"axios": "*"}, "devDependencies": {"@babel/cli": "^7.4.4", "@babel/preset-env": "^7.4.5", "@types/mocha": "^9.0.0", "@types/sinon": "^10.0.0", "@types/node": "^14.0.0", "axios": "^0.21.0", "babel-plugin-transform-es2015-modules-umd": "^6.24.1", "c8": "^7.0.0", "gts": "^3.0.0", "js-green-licenses": "^3.0.0", "microbundle": "^0.11.0", "mocha": "^9.0.0", "nock": "^13.0.0", "semantic-release": "^17.0.4", "sinon": "^11.0.0", "typescript": "~4.3.0"}, "files": ["dist"], "c8": {"exclude": ["build/test", "dist"]}, "browserslist": "> 1%, last 2 versions, Firefox ESR"}
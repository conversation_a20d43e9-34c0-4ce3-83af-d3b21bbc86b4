{"name": "rollup", "version": "4.24.4", "description": "Next-generation ES module bundler", "main": "dist/rollup.js", "module": "dist/es/rollup.js", "types": "dist/rollup.d.ts", "bin": {"rollup": "dist/bin/rollup"}, "napi": {"name": "rollup", "package": {"name": "@rollup/rollup"}, "triples": {"defaults": false, "additional": ["aarch64-apple-darwin", "aarch64-linux-android", "aarch64-pc-windows-msvc", "aarch64-unknown-freebsd", "aarch64-unknown-linux-gnu", "aarch64-unknown-linux-musl", "armv7-linux-androideabi", "armv7-unknown-linux-gnueabihf", "armv7-unknown-linux-musleabihf", "i686-pc-windows-msvc", "riscv64gc-unknown-linux-gnu", "powerpc64le-unknown-linux-gnu", "s390x-unknown-linux-gnu", "x86_64-apple-darwin", "x86_64-pc-windows-msvc", "x86_64-unknown-freebsd", "x86_64-unknown-linux-gnu", "x86_64-unknown-linux-musl"]}}, "scripts": {"build": "concurrently -c green,blue \"npm run build:wasm\" \"npm:build:ast-converters\" && concurrently -c green,blue \"npm run build:napi -- --release\" \"npm:build:js\" && npm run build:copy-native", "build:quick": "concurrently -c green,blue 'npm:build:napi' 'npm:build:cjs' && npm run build:copy-native", "build:napi": "napi build --platform --dts native.d.ts --js false --cargo-cwd rust -p bindings_napi --cargo-name bindings_napi", "build:wasm": "cross-env RUSTFLAGS=\"-C opt-level=z\" wasm-pack build rust/bindings_wasm --out-dir ../../wasm --target web --no-pack && shx rm wasm/.gitignore", "build:wasm:node": "wasm-pack build rust/bindings_wasm --out-dir ../../wasm-node --target nodejs --no-pack && shx rm wasm-node/.gitignore", "update:napi": "npm run build:napi && npm run build:copy-native", "build:js": "rollup --config rollup.config.ts --configPlugin typescript --forceExit", "build:js:node": "rollup --config rollup.config.ts --configPlugin typescript --configIsBuildNode --forceExit", "build:prepare": "concurrently -c green,blue \"npm run build:napi -- --release\" \"npm:build:js:node\" && npm run build:copy-native", "update:js": "npm run build:js && npm run build:copy-native", "build:copy-native": "shx mkdir -p dist && shx cp rollup.*.node dist/", "dev": "concurrently -kc green,blue 'nodemon --watch rust -e rs --exec \"npm run build:wasm\"' 'vitepress dev docs'", "build:cjs": "rollup --config rollup.config.ts --configPlugin typescript --configTest --forceExit", "build:bootstrap": "shx mv dist dist-build && node dist-build/bin/rollup --config rollup.config.ts --configPlugin typescript --forceExit && shx rm -rf dist-build", "build:bootstrap:cjs": "shx mv dist dist-build && node dist-build/bin/rollup --config rollup.config.ts --configPlugin typescript --configTest --forceExit && shx rm -rf dist-build", "build:docs": "vitepress build docs", "build:ast-converters": "node scripts/generate-ast-converters.js", "preview:docs": "vitepress preview docs", "ci:artifacts": "napi artifacts", "ci:lint": "concurrently -c red,yellow,green,blue 'npm:lint:js:nofix' 'npm:lint:native-js' 'npm:lint:markdown:nofix' 'npm:lint:rust:nofix'", "ci:test:only": "npm run build:cjs && npm run build:copy-native && npm run build:bootstrap && npm run build:copy-native && npm run test:only", "ci:test:all": "npm run build:cjs && npm run build:copy-native && npm run build:bootstrap && npm run build:copy-native && concurrently --kill-others-on-fail -c green,blue,magenta,cyan 'npm:test:only' 'npm:test:typescript' 'npm:test:leak' 'npm:test:browser'", "ci:coverage": "npm run build:cjs && npm run build:copy-native && npm run build:bootstrap && npm run build:copy-native && nyc --reporter l<PERSON><PERSON><PERSON> mocha", "lint": "concurrently -c red,yellow,green,blue 'npm:lint:js' 'npm:lint:native-js' 'npm:lint:markdown' 'npm:lint:rust'", "lint:js": "eslint . --fix --cache", "lint:js:nofix": "eslint . --cache", "lint:native-js": "node scripts/lint-native-js.js", "lint:markdown": "prettier --write \"**/*.md\"", "lint:markdown:nofix": "prettier --check \"**/*.md\"", "lint:rust": "cd rust && cargo fmt && cargo clippy --fix --allow-dirty", "lint:rust:nofix": "cd rust && cargo fmt --check && cargo clippy", "perf": "npm run build:bootstrap:cjs && node --expose-gc scripts/perf-report/index.js", "prepare": "husky && node scripts/check-release.js || npm run build:prepare", "prepublishOnly": "node scripts/check-release.js && node scripts/prepublish.js", "postpublish": "node scripts/postpublish.js", "prepublish:napi": "napi prepublish --skip-gh-release", "release": "node scripts/prepare-release.js", "release:docs": "git fetch --update-head-ok origin master:master && git branch --force documentation-published master && git push origin documentation-published", "check-audit": "check-audit", "resolve-audit": "resolve-audit", "test": "npm run build && npm run test:all", "test:update-snapshots": "node scripts/update-snapshots.js", "test:cjs": "npm run build:cjs && npm run test:only", "test:quick": "mocha -b test/test.js", "test:all": "concurrently --kill-others-on-fail -c green,blue,magenta,cyan,red 'npm:test:only' 'npm:test:browser' 'npm:test:typescript' 'npm:test:package' 'npm:test:options'", "test:coverage": "npm run build:cjs && shx rm -rf coverage/* && nyc --reporter html mocha test/test.js", "test:coverage:browser": "npm run build && shx rm -rf coverage/* && nyc mocha test/browser/index.js", "test:leak": "npm install --no-save weak-napi && node --expose-gc test/leak/index.js", "test:package": "node scripts/test-package.js", "test:options": "node scripts/test-options.js", "test:only": "mocha test/test.js", "test:typescript": "shx rm -rf test/typescript/dist && shx cp -r dist test/typescript/ && tsc --noEmit -p test/typescript && tsc --noEmit && tsc --noEmit -p scripts", "test:browser": "mocha test/browser/index.js", "watch": "rollup --config rollup.config.ts --configPlugin typescript --watch"}, "repository": "rollup/rollup", "keywords": ["modules", "bundler", "bundling", "es6", "optimizer"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "homepage": "https://rollupjs.org/", "optionalDependencies": {"fsevents": "~2.3.2", "@rollup/rollup-darwin-arm64": "4.24.4", "@rollup/rollup-android-arm64": "4.24.4", "@rollup/rollup-win32-arm64-msvc": "4.24.4", "@rollup/rollup-freebsd-arm64": "4.24.4", "@rollup/rollup-linux-arm64-gnu": "4.24.4", "@rollup/rollup-linux-arm64-musl": "4.24.4", "@rollup/rollup-android-arm-eabi": "4.24.4", "@rollup/rollup-linux-arm-gnueabihf": "4.24.4", "@rollup/rollup-linux-arm-musleabihf": "4.24.4", "@rollup/rollup-win32-ia32-msvc": "4.24.4", "@rollup/rollup-linux-riscv64-gnu": "4.24.4", "@rollup/rollup-linux-powerpc64le-gnu": "4.24.4", "@rollup/rollup-linux-s390x-gnu": "4.24.4", "@rollup/rollup-darwin-x64": "4.24.4", "@rollup/rollup-win32-x64-msvc": "4.24.4", "@rollup/rollup-freebsd-x64": "4.24.4", "@rollup/rollup-linux-x64-gnu": "4.24.4", "@rollup/rollup-linux-x64-musl": "4.24.4"}, "dependencies": {"@types/estree": "1.0.6"}, "devDependenciesComments": {"core-js": "We only update manually as every update requires a snapshot update"}, "devDependencies": {"@codemirror/commands": "^6.7.1", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/language": "^6.10.3", "@codemirror/search": "^6.5.7", "@codemirror/state": "^6.4.1", "@codemirror/view": "^6.34.1", "@eslint/js": "^9.13.0", "@inquirer/prompts": "^7.0.1", "@jridgewell/sourcemap-codec": "^1.5.0", "@mermaid-js/mermaid-cli": "^11.2.1", "@napi-rs/cli": "^2.18.4", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-buble": "^1.0.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-replace": "^6.0.1", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.1", "@rollup/pluginutils": "^5.1.3", "@shikijs/vitepress-twoslash": "^1.22.2", "@types/mocha": "^10.0.9", "@types/node": "^18.19.63", "@types/semver": "^7.5.8", "@types/yargs-parser": "^21.0.3", "@vue/language-server": "^2.1.10", "acorn": "^8.14.0", "acorn-import-assertions": "^1.9.0", "acorn-jsx": "^5.3.2", "buble": "^0.20.0", "builtin-modules": "^4.0.0", "chokidar": "^3.6.0", "colorette": "^2.0.20", "concurrently": "^9.0.1", "core-js": "3.38.1", "cross-env": "^7.0.3", "date-time": "^4.0.0", "es5-shim": "^4.6.7", "es6-shim": "^0.35.8", "eslint": "^9.13.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-unicorn": "^56.0.0", "eslint-plugin-vue": "^9.30.0", "fixturify": "^3.0.0", "flru": "^1.0.2", "fs-extra": "^11.2.0", "github-api": "^3.4.0", "globals": "^15.11.0", "husky": "^9.1.6", "is-reference": "^3.0.2", "lint-staged": "^15.2.10", "locate-character": "^3.0.0", "magic-string": "^0.30.12", "mocha": "^10.8.2", "nodemon": "^3.1.7", "npm-audit-resolver": "^3.0.0-RC.0", "nyc": "^17.1.0", "pinia": "^2.2.5", "prettier": "^3.3.3", "prettier-plugin-organize-imports": "^4.1.0", "pretty-bytes": "^6.1.1", "pretty-ms": "^9.1.0", "requirejs": "^2.3.7", "rollup": "^4.24.3", "rollup-plugin-license": "^3.5.3", "rollup-plugin-string": "^3.0.0", "semver": "^7.6.3", "shx": "^0.3.4", "signal-exit": "^4.1.0", "source-map": "^0.7.4", "source-map-support": "^0.5.21", "systemjs": "^6.15.1", "terser": "^5.36.0", "tslib": "^2.8.1", "typescript": "^5.6.3", "typescript-eslint": "^8.12.2", "vite": "^5.4.10", "vitepress": "^1.4.3", "vue": "^3.5.12", "vue-tsc": "^2.1.10", "wasm-pack": "^0.13.1", "yargs-parser": "^21.1.1"}, "overrides": {"axios": "^1.7.7", "semver": "^7.6.3", "ws": "^8.18.0"}, "comments": {"vue-tsc": "This is necessary so that prettier-plugin-organize-imports works correctly in Vue templatges", "ws": "mermaid requires an older 8.13.0 version via puppeteer with vulnerabilities"}, "files": ["dist/*.node", "dist/**/*.js", "dist/*.d.ts", "dist/bin/rollup", "dist/es/package.json"], "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "exports": {".": {"types": "./dist/rollup.d.ts", "require": "./dist/rollup.js", "import": "./dist/es/rollup.js"}, "./loadConfigFile": {"types": "./dist/loadConfigFile.d.ts", "require": "./dist/loadConfigFile.js", "default": "./dist/loadConfigFile.js"}, "./getLogFilter": {"types": "./dist/getLogFilter.d.ts", "require": "./dist/getLogFilter.js", "import": "./dist/es/getLogFilter.js"}, "./parseAst": {"types": "./dist/parseAst.d.ts", "require": "./dist/parseAst.js", "import": "./dist/es/parseAst.js"}, "./dist/*": "./dist/*"}}